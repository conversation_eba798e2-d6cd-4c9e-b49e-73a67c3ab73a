# GhostCursor - 人性化鼠标模拟器

GhostCursor是一个基于Playwright的鼠标和键盘操作模拟器，旨在创建更加自然和人性化的自动化操作。

## 功能特性

### 🖱️ 自然鼠标移动
- **贝塞尔曲线路径**: 使用贝塞尔曲线生成自然的鼠标移动轨迹
- **随机控制点**: 自动生成随机控制点，避免机械化的直线移动
- **速度变化**: 根据移动距离自动调整移动时间
- **微小抖动**: 添加人手自然的微小抖动效果

### 🎯 智能点击操作
- **精确定位**: 自动获取元素中心坐标
- **随机偏移**: 添加随机偏移，模拟人手的不精确性
- **点击延时**: 模拟真实的按下和释放时间
- **双击支持**: 支持双击操作，包含合理的间隔时间

### ⌨️ 人性化输入
- **变速打字**: 每个字符的输入速度略有不同
- **思考停顿**: 随机添加停顿，模拟思考时间
- **自然节奏**: 符合人类打字习惯的输入节奏

### 🎲 随机行为
- **随机移动**: 模拟用户的无意识鼠标移动
- **滚轮操作**: 自然的页面滚动效果
- **行为变化**: 每次操作都略有不同，避免被检测

## 快速开始

### 1. 基本使用

```python
from playwright_framework import PlayWrightFrameWork
from tool import Log

class MyAutomation(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log())
    
    def ok(self):
        return True
    
    def worker(self):
        # 启动浏览器后，ghost_cursor会自动初始化
        page = self.page
        ghost = self.ghost_cursor
        
        # 访问网页
        page.goto("https://example.com")
        
        # 自然移动到元素
        ghost.move_to_element('input[name="email"]')
        
        # 点击元素
        ghost.click_element('input[name="email"]')
        
        # 人性化输入
        ghost.human_type("<EMAIL>")
        
        # 点击按钮
        ghost.click_element('button[type="submit"]')

# 使用示例
with MyAutomation() as automation:
    if automation.new_chrome_browser():
        automation.worker()
```

### 2. 高级功能

```python
# 精确坐标操作
ghost.move_to(400, 300, duration=1.5)
ghost.click(400, 300)

# 双击操作
ghost.double_click(500, 400)

# 滚动页面
ghost.scroll(delta_y=-200, steps=5)

# 随机移动（模拟浏览行为）
ghost.random_movement(duration=3.0, movement_range=100)

# 带偏移的元素点击
ghost.click_element('#button', offset_x=5, offset_y=-2)

# 自定义打字速度
ghost.human_type("Hello World", typing_speed=0.1)
```

## API 参考

### 鼠标移动

#### `move_to(x, y, duration=None)`
移动鼠标到指定坐标
- `x, y`: 目标坐标
- `duration`: 移动时间（秒），None时自动计算

#### `move_to_element(selector, duration=None)`
移动鼠标到指定元素中心
- `selector`: CSS选择器
- `duration`: 移动时间（秒）

### 点击操作

#### `click(x=None, y=None, button='left', delay=None)`
在指定位置点击
- `x, y`: 点击坐标，None时使用当前位置
- `button`: 鼠标按键（'left', 'right', 'middle'）
- `delay`: 按下到释放的延时

#### `click_element(selector, button='left', offset_x=0, offset_y=0)`
点击指定元素
- `selector`: CSS选择器
- `button`: 鼠标按键
- `offset_x, offset_y`: 相对元素中心的偏移

#### `double_click(x=None, y=None)`
双击操作

### 输入操作

#### `human_type(text, selector=None, typing_speed=None)`
模拟人类打字
- `text`: 要输入的文本
- `selector`: 目标元素选择器（可选）
- `typing_speed`: 打字速度（每字符秒数）

### 其他操作

#### `scroll(delta_x=0, delta_y=-100, steps=3)`
滚动页面
- `delta_x, delta_y`: 滚动距离
- `steps`: 分步滚动次数

#### `random_movement(duration=2.0, movement_range=50)`
随机鼠标移动
- `duration`: 移动持续时间
- `movement_range`: 移动范围（像素）

## 集成到现有项目

GhostCursor已经集成到`PlayWrightFrameWork`中，在创建浏览器实例时会自动初始化：

```python
# 在macys.py中的使用示例
class MACYS(PlayWrightFrameWork):
    def worker(self):
        # ghost_cursor会在new_chrome_browser()后自动可用
        if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
            # 使用GhostCursor进行自然点击
            self.ghost_cursor.click_element(button_selector)
            # 使用GhostCursor进行自然输入
            self.ghost_cursor.human_type(self.email)
        else:
            # 降级到普通操作
            self.page.click(button_selector)
            self.page.keyboard.type(self.email)
```

## 运行演示

运行演示程序查看GhostCursor的各种功能：

```bash
python ghost_cursor_example.py
```

演示程序将展示：
- 基本的鼠标移动和点击
- 自然的文本输入
- 表单交互
- 高级操作功能

## 注意事项

1. **性能考虑**: 自然的移动和输入会比直接操作慢一些
2. **检测规避**: 主要用于规避自动化检测，提高成功率
3. **参数调整**: 可以根据需要调整移动速度、输入速度等参数
4. **兼容性**: 基于Playwright，支持Chrome、Firefox等主流浏览器

## 技术原理

- **贝塞尔曲线**: 使用三次贝塞尔曲线生成平滑的移动路径
- **随机化**: 在关键参数中添加随机因素
- **时间模拟**: 模拟人类操作的时间特征
- **坐标抖动**: 添加微小的坐标偏移

GhostCursor让您的自动化操作更加自然和难以检测！
