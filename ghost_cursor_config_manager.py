"""
GhostCursor配置管理工具
用于管理config.py中的GhostCursor相关配置
"""

import re
import os


def read_config():
    """读取当前配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找GhostCursor启用配置
        enabled_match = re.search(r'ghost_cursor_enabled\s*=\s*(True|False)', content)
        enabled = enabled_match.group(1) == 'True' if enabled_match else True
        
        # 查找快速模式配置
        fast_match = re.search(r'ghost_cursor_fast_mode\s*=\s*(True|False)', content)
        fast_mode = fast_match.group(1) == 'True' if fast_match else True
        
        return {
            'enabled': enabled,
            'fast_mode': fast_mode
        }
    except Exception as e:
        print(f"读取配置文件失败: {e}")
        return None


def write_config(enabled=None, fast_mode=None):
    """写入新配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新启用配置
        if enabled is not None:
            enabled_value = 'True' if enabled else 'False'
            pattern = r'(ghost_cursor_enabled\s*=\s*)(True|False)'
            replacement = f'\\g<1>{enabled_value}'
            content = re.sub(pattern, replacement, content)
        
        # 更新快速模式配置
        if fast_mode is not None:
            fast_value = 'True' if fast_mode else 'False'
            pattern = r'(ghost_cursor_fast_mode\s*=\s*)(True|False)'
            replacement = f'\\g<1>{fast_value}'
            content = re.sub(pattern, replacement, content)
        
        # 写回文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"写入配置文件失败: {e}")
        return False


def show_current_config():
    """显示当前配置"""
    config = read_config()
    if config:
        print("当前GhostCursor配置:")
        print(f"  启用状态: ghost_cursor_enabled = {config['enabled']} ({'启用' if config['enabled'] else '禁用'})")
        print(f"  快速模式: ghost_cursor_fast_mode = {config['fast_mode']} ({'快速' if config['fast_mode'] else '自然'})")
    else:
        print("无法读取当前配置")


def set_ghost_cursor_enabled(enabled):
    """设置GhostCursor启用状态"""
    if write_config(enabled=enabled):
        status_text = "启用" if enabled else "禁用"
        print(f"✅ GhostCursor已{status_text}")
        if enabled:
            print("💡 GhostCursor启用后的特点:")
            print("   - 模拟人类鼠标移动轨迹")
            print("   - 自然的点击和输入行为")
            print("   - 更难被检测为机器人")
        else:
            print("💡 GhostCursor禁用后:")
            print("   - 使用Playwright原生点击")
            print("   - 速度更快但可能被检测")
    else:
        print("❌ 设置失败")


def set_fast_mode(fast_mode):
    """设置快速模式"""
    if write_config(fast_mode=fast_mode):
        mode_text = "快速模式" if fast_mode else "自然模式"
        print(f"✅ 已设置为{mode_text}")
        if fast_mode:
            print("💡 快速模式特点:")
            print("   - 鼠标移动速度快 (0.05-0.3秒)")
            print("   - 减少路径点数和延时")
            print("   - 适合自动化任务")
        else:
            print("💡 自然模式特点:")
            print("   - 鼠标移动更自然 (0.1-0.8秒)")
            print("   - 更多路径点和随机延时")
            print("   - 更难被检测为机器人")
    else:
        print("❌ 设置失败")


def toggle_ghost_cursor():
    """切换GhostCursor启用状态"""
    config = read_config()
    if config:
        new_enabled = not config['enabled']
        set_ghost_cursor_enabled(new_enabled)
    else:
        print("❌ 无法读取当前配置，切换失败")


def toggle_fast_mode():
    """切换快速模式"""
    config = read_config()
    if config:
        new_fast_mode = not config['fast_mode']
        set_fast_mode(new_fast_mode)
    else:
        print("❌ 无法读取当前配置，切换失败")


def set_preset_config(preset):
    """设置预设配置"""
    presets = {
        'performance': {'enabled': True, 'fast_mode': True},    # 性能优先
        'stealth': {'enabled': True, 'fast_mode': False},       # 隐蔽优先
        'native': {'enabled': False, 'fast_mode': True}         # 原生点击
    }
    
    if preset in presets:
        config = presets[preset]
        if write_config(enabled=config['enabled'], fast_mode=config['fast_mode']):
            print(f"✅ 已应用{preset}预设配置")
            show_current_config()
        else:
            print("❌ 应用预设配置失败")
    else:
        print("❌ 未知的预设配置")


def main():
    """主函数"""
    print("=== GhostCursor配置管理工具 ===")
    print()
    
    # 显示当前配置
    show_current_config()
    print()
    
    while True:
        print("请选择操作:")
        print("1. 启用GhostCursor")
        print("2. 禁用GhostCursor")
        print("3. 切换GhostCursor状态")
        print("4. 启用快速模式")
        print("5. 启用自然模式")
        print("6. 切换快速模式")
        print("7. 预设配置")
        print("8. 查看当前配置")
        print("9. 退出")
        print()
        
        try:
            choice = input("请输入选择 (1-9): ").strip()
            print()
            
            if choice == '1':
                set_ghost_cursor_enabled(True)
                
            elif choice == '2':
                set_ghost_cursor_enabled(False)
                
            elif choice == '3':
                toggle_ghost_cursor()
                
            elif choice == '4':
                set_fast_mode(True)
                
            elif choice == '5':
                set_fast_mode(False)
                
            elif choice == '6':
                toggle_fast_mode()
                
            elif choice == '7':
                print("预设配置:")
                print("  a. performance - 性能优先 (启用GhostCursor + 快速模式)")
                print("  b. stealth - 隐蔽优先 (启用GhostCursor + 自然模式)")
                print("  c. native - 原生点击 (禁用GhostCursor)")
                preset = input("请选择预设 (a/b/c): ").strip().lower()
                
                preset_map = {'a': 'performance', 'b': 'stealth', 'c': 'native'}
                if preset in preset_map:
                    set_preset_config(preset_map[preset])
                else:
                    print("❌ 无效选择")
                
            elif choice == '8':
                show_current_config()
                
            elif choice == '9':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入1-9")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作出错: {e}")


if __name__ == "__main__":
    main()
