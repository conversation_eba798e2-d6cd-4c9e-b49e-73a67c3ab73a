import random
import math
import time
from typing import <PERSON><PERSON>, List, Optional
from playwright.sync_api import Page
from tool import Log


class GhostCursor:
    """
    模拟人类鼠标行为的类，包括自然的移动轨迹、点击和输入
    """
    
    def __init__(self, page: Page, log_instance: Log = None, fast_mode: bool = True):
        self.page = page
        self.log = log_instance or Log()
        self.current_x = 0
        self.current_y = 0
        self.viewport_size = self.page.viewport_size
        self.fast_mode = fast_mode  # 快速模式开关

        # 初始化鼠标位置到屏幕中央
        if self.viewport_size:
            self.current_x = self.viewport_size['width'] // 2
            self.current_y = self.viewport_size['height'] // 2

        if self.fast_mode:
            self.log.debug("GhostCursor启用快速模式")
    
    def _bezier_curve(self, start: Tuple[int, int], end: Tuple[int, int],
                     control1: Optional[Tuple[int, int]] = None,
                     control2: Optional[Tuple[int, int]] = None,
                     steps: int = 20) -> List[Tuple[int, int]]:
        """
        生成贝塞尔曲线路径点
        """
        x1, y1 = start
        x4, y4 = end
        
        # 如果没有提供控制点，自动生成
        if not control1 or not control2:
            # 计算距离和角度
            distance = math.sqrt((x4 - x1) ** 2 + (y4 - y1) ** 2)
            
            # 生成随机控制点，模拟自然的鼠标移动
            offset = distance * 0.3
            angle1 = random.uniform(-math.pi/4, math.pi/4)
            angle2 = random.uniform(-math.pi/4, math.pi/4)
            
            mid_x = (x1 + x4) / 2
            mid_y = (y1 + y4) / 2
            
            control1 = (
                int(mid_x + offset * math.cos(angle1)),
                int(mid_y + offset * math.sin(angle1))
            )
            control2 = (
                int(mid_x + offset * math.cos(angle2)),
                int(mid_y + offset * math.sin(angle2))
            )
        
        x2, y2 = control1
        x3, y3 = control2
        
        points = []
        for i in range(steps + 1):
            t = i / steps
            
            # 贝塞尔曲线公式
            x = (1-t)**3 * x1 + 3*(1-t)**2*t * x2 + 3*(1-t)*t**2 * x3 + t**3 * x4
            y = (1-t)**3 * y1 + 3*(1-t)**2*t * y2 + 3*(1-t)*t**2 * y3 + t**3 * y4
            
            points.append((int(x), int(y)))
        
        return points
    
    def _get_element_center(self, selector: str) -> Tuple[int, int]:
        """
        获取元素的中心坐标
        """
        try:
            element = self.page.locator(selector).first
            box = element.bounding_box()
            if box:
                center_x = int(box['x'] + box['width'] / 2)
                center_y = int(box['y'] + box['height'] / 2)
                return center_x, center_y
        except Exception as e:
            self.log.err(f"获取元素坐标失败: {e}")
        
        # 如果获取失败，返回当前位置
        return self.current_x, self.current_y
    
    def _add_jitter(self, x: int, y: int, jitter_range: int = 3) -> Tuple[int, int]:
        """
        添加微小的随机偏移，模拟人手的不精确性
        """
        jitter_x = random.randint(-jitter_range, jitter_range)
        jitter_y = random.randint(-jitter_range, jitter_range)
        return x + jitter_x, y + jitter_y
    
    def move_to(self, x: int, y: int, duration: float = None) -> None:
        """
        移动鼠标到指定坐标
        """
        if duration is None:
            # 根据距离计算移动时间
            distance = math.sqrt((x - self.current_x) ** 2 + (y - self.current_y) ** 2)
            if self.fast_mode:
                # 快速模式：大幅减少移动时间
                duration = max(0.05, min(0.3, distance / 2000))  # 0.05-0.3秒，非常快
            else:
                # 普通模式：稍微减少移动时间
                duration = max(0.1, min(0.8, distance / 1000))  # 0.1-0.8秒

        # 生成移动路径
        steps = 10 if self.fast_mode else 20  # 快速模式使用更少的路径点
        path = self._bezier_curve((self.current_x, self.current_y), (x, y), steps=steps)

        # 计算每步的延时
        step_delay = max(0.001, duration / len(path))

        try:
            for point_x, point_y in path:
                # 添加微小抖动
                jitter_range = 0 if self.fast_mode else 1  # 快速模式减少抖动
                jitter_x, jitter_y = self._add_jitter(point_x, point_y, jitter_range)

                # 移动鼠标
                self.page.mouse.move(jitter_x, jitter_y)

                # 延时
                if self.fast_mode:
                    # 快速模式：最小延时
                    random_delay = max(0.001, step_delay)
                else:
                    # 普通模式：稍微随机延时
                    random_delay = max(0.001, step_delay + random.uniform(-0.002, 0.003))
                time.sleep(random_delay)

            self.current_x, self.current_y = x, y
            self.log.debug(f"鼠标移动到: ({x}, {y})")

        except Exception as e:
            self.log.err(f"鼠标移动失败: {e}")
    
    def move_to_element(self, selector: str, duration: float = None) -> None:
        """
        移动鼠标到指定元素
        """
        target_x, target_y = self._get_element_center(selector)
        self.move_to(target_x, target_y, duration)
    
    def click(self, x: int = None, y: int = None, button: str = 'left',
              delay: float = None) -> None:
        """
        在指定位置点击鼠标
        """
        if x is not None and y is not None:
            self.move_to(x, y)

        if delay is None:
            delay = random.uniform(0.02, 0.08)  # 减少点击延时，从0.05-0.15减少到0.02-0.08

        try:
            # 减少点击前停顿
            time.sleep(random.uniform(0.01, 0.03))  # 从0.02-0.08减少到0.01-0.03

            # 按下
            self.page.mouse.down(button=button)
            time.sleep(delay)

            # 释放
            self.page.mouse.up(button=button)

            self.log.debug(f"鼠标点击: ({self.current_x}, {self.current_y})")

        except Exception as e:
            self.log.err(f"鼠标点击失败: {e}")
    
    def click_element(self, selector: str, button: str = 'left',
                     offset_x: int = 0, offset_y: int = 0, force: bool = False) -> None:
        """
        点击指定元素
        """
        try:
            # 首先尝试等待元素可见
            element = self.page.locator(selector).first
            if not force:
                element.wait_for(state="visible", timeout=5000)

            # 检查元素是否可点击
            if element.is_visible():
                # 滚动到元素可见区域
                element.scroll_into_view_if_needed()

                # 获取元素中心坐标
                target_x, target_y = self._get_element_center(selector)

                # 如果坐标获取失败，使用Playwright的直接点击
                if target_x == self.current_x and target_y == self.current_y:
                    self.log.debug(f"坐标获取失败，使用直接点击: {selector}")
                    element.click(force=force)
                    return

                # 添加偏移和随机抖动
                final_x = target_x + offset_x + random.randint(-2, 2)
                final_y = target_y + offset_y + random.randint(-2, 2)

                # 快速移动并点击 - 避免重复移动
                self.move_to(final_x, final_y, duration=0.2)  # 移动到目标位置
                time.sleep(random.uniform(0.05, 0.1))  # 到达后短暂停顿
                self.click(button=button)  # 直接点击，不传坐标避免重复移动
            else:
                self.log.err(f"元素不可见: {selector}")
                # 降级到直接点击
                element.click(force=True)

        except Exception as e:
            self.log.err(f"点击元素失败 {selector}: {e}")
            # 降级到Playwright原生点击
            try:
                self.page.locator(selector).first.click(force=True)
                self.log.debug(f"降级点击成功: {selector}")
            except Exception as e2:
                self.log.err(f"降级点击也失败: {e2}")
    
    def double_click(self, x: int = None, y: int = None) -> None:
        """
        双击
        """
        if x is not None and y is not None:
            self.move_to(x, y)
        
        try:
            # 第一次点击
            self.click()
            
            # 双击间隔
            time.sleep(random.uniform(0.05, 0.12))
            
            # 第二次点击
            self.click()
            
            self.log.debug(f"鼠标双击: ({self.current_x}, {self.current_y})")
            
        except Exception as e:
            self.log.err(f"鼠标双击失败: {e}")
    
    def safe_click_element(self, selector: str, max_attempts: int = 3) -> bool:
        """
        安全的元素点击，包含多种降级策略
        """
        self.log.debug(f"开始安全点击元素: {selector}")

        # 首先检查元素是否存在
        try:
            element_count = self.page.locator(selector).count()
            self.log.debug(f"找到 {element_count} 个匹配的元素")
            if element_count == 0:
                self.log.err(f"未找到元素: {selector}")
                return False
        except Exception as e:
            self.log.err(f"检查元素存在性失败: {e}")
            return False

        for attempt in range(max_attempts):
            try:
                self.log.debug(f"尝试点击元素 (第{attempt + 1}次): {selector}")

                # 方法1: 使用GhostCursor点击（不强制）
                if attempt == 0:
                    self.log.debug("方法1: 使用GhostCursor正常点击")
                    self.click_element(selector, force=False)
                    self.log.success(f"GhostCursor正常点击成功: {selector}")
                    return True

                # 方法2: 强制使用GhostCursor点击
                elif attempt == 1:
                    self.log.debug("方法2: 使用GhostCursor强制点击")
                    self.click_element(selector, force=True)
                    self.log.success(f"GhostCursor强制点击成功: {selector}")
                    return True

                # 方法3: 降级到Playwright原生点击
                else:
                    self.log.debug("方法3: 降级到Playwright原生点击")
                    element = self.page.locator(selector).first

                    # 尝试等待元素可见
                    try:
                        element.wait_for(state="visible", timeout=2000)
                        self.log.debug("元素已可见")
                    except:
                        self.log.debug("元素等待超时，尝试强制点击")

                    # 尝试滚动到元素
                    try:
                        element.scroll_into_view_if_needed(timeout=1000)
                        self.log.debug("已滚动到元素")
                    except:
                        self.log.debug("滚动到元素失败")

                    # 强制点击
                    element.click(force=True, timeout=5000)
                    self.log.success(f"Playwright原生点击成功: {selector}")
                    return True

            except Exception as e:
                self.log.err(f"第{attempt + 1}次点击失败: {str(e)[:200]}...")
                if attempt < max_attempts - 1:
                    self.log.debug(f"等待0.5秒后重试...")
                    time.sleep(0.5)
                continue

        self.log.err(f"所有{max_attempts}次点击尝试都失败了: {selector}")
        return False

    def human_type(self, text: str, selector: str = None,
                   typing_speed: float = None) -> None:
        """
        模拟人类打字
        """
        if selector:
            self.click_element(selector)
            time.sleep(random.uniform(0.1, 0.3))

        if typing_speed is None:
            typing_speed = random.uniform(0.08, 0.15)  # 每个字符的平均间隔

        try:
            for char in text:
                # 随机打字速度
                char_delay = max(0.01, typing_speed + random.uniform(-0.03, 0.05))

                # 模拟偶尔的停顿（思考时间）
                if random.random() < 0.1:  # 10%概率
                    char_delay += random.uniform(0.2, 0.8)

                self.page.keyboard.type(char, delay=char_delay * 1000)

            self.log.debug(f"输入文本: {text}")

        except Exception as e:
            self.log.err(f"输入文本失败: {e}")
    
    def scroll(self, delta_x: int = 0, delta_y: int = -100, 
               steps: int = 3) -> None:
        """
        模拟鼠标滚轮滚动
        """
        try:
            step_x = delta_x / steps
            step_y = delta_y / steps
            
            for _ in range(steps):
                self.page.mouse.wheel(step_x, step_y)
                time.sleep(random.uniform(0.05, 0.15))
            
            self.log.debug(f"鼠标滚动: ({delta_x}, {delta_y})")
            
        except Exception as e:
            self.log.err(f"鼠标滚动失败: {e}")
    
    def random_movement(self, duration: float = 2.0, 
                       movement_range: int = 50) -> None:
        """
        随机鼠标移动，模拟人类的无意识移动
        """
        if not self.viewport_size:
            return
        
        end_time = time.time() + duration
        
        while time.time() < end_time:
            # 在当前位置附近随机移动
            random_x = self.current_x + random.randint(-movement_range, movement_range)
            random_y = self.current_y + random.randint(-movement_range, movement_range)
            
            # 确保不超出屏幕边界
            random_x = max(10, min(self.viewport_size['width'] - 10, random_x))
            random_y = max(10, min(self.viewport_size['height'] - 10, random_y))
            
            self.move_to(random_x, random_y, random.uniform(0.3, 0.8))
            time.sleep(random.uniform(0.2, 0.6))
