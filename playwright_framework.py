from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from abc import ABC, abstractmethod
from tool import Log, check_ip
from ghost_cursor import GhostCursor
import config

__all__ = [
    "PlayWrightFrameWork"
]


'''https://bot.sannysoft.com/'''
class PlayWrightFrameWork(ABC):
    def __init__(self, log_instance: Log=None) -> None:
        self.playwright = sync_playwright().start()
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.page_hook = self._get_page
        self.ghost_cursor: GhostCursor = None

        if log_instance:
            self.log = log_instance
        else:
            self.log = Log()

        # 15000ms
        self.timeout = 15000

        self.cnt_ok = 0

        self.domain = ''
    
    @abstractmethod
    def worker(self) -> bool:
        '''工作函数'''
        ...
    
    @abstractmethod
    def ok(self) -> bool:
        '''判断此次结果是否ok'''
        ...
    
    def cleanup(self):
        try:
            if self.page:
                self.page.close(run_before_unload=False)
                self.page = None
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork.cleanup] {e}')
        try:
            if self.context:
                self.context.close()
                self.context = None
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork.cleanup] {e}')
        try:
            if self.browser:
                self.browser.close()
                self.browser = None
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork.cleanup] {e}')
        try:
            self.playwright.stop()
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork.cleanup] {e}')
        self.log.notice('cleanup.')
    
    def clear_cookie_and_new_page(self):
        self.log.debug('!call PlayWrightFrameWork.clear_cookie_and_new_page')
        try:
            self.context.clear_cookies()
            self.page_hook()
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork.clear_cookie_and_new_page] {e}')
    
    def destroy_page(self):
        '''
        close the page immediately
        '''
        self.log.debug('!call PlayWrightFrameWork.destroy_page')
        if self.page_hook == self._new_page:
            self.page.close(run_before_unload=True)
    
    def _new_page(self):
        '''
        new page
        '''
        self.log.debug('!call PlayWrightFrameWork._new_page')
        try:
            self.page.close(run_before_unload=True)
            self.page = self.context.new_page()
        except Exception as e:
            self.log.err(f'[PlayWrightFrameWork._new_page] {e}')
    
    def _get_page(self):
        '''
        get page, only cdp connect
        '''
        self.log.debug('!call PlayWrightFrameWork._get_page')
        self.page = self.context.pages[0]
    
    def new_firefox_browser(self, proxy=None) -> bool:
        if proxy:
            if not check_ip(proxy['server'], log_instance=self.log):
                return False
        self.browser = self.playwright.firefox.launch(headless=False, proxy=proxy, timeout=self.timeout)
        self.context = self.browser.new_context(ignore_https_errors=True, has_touch=True)
        self.page = self.context.new_page()
        self.page_hook = self._new_page
        # 初始化GhostCursor（从config读取配置）
        ghost_cursor_enabled = getattr(config, 'ghost_cursor_enabled', True)
        if ghost_cursor_enabled:
            fast_mode = getattr(config, 'ghost_cursor_fast_mode', True)
            self.ghost_cursor = GhostCursor(self.page, self.log, fast_mode=fast_mode)
            self.log.debug(f"GhostCursor已启用，快速模式: {'启用' if fast_mode else '禁用'}")
        else:
            self.ghost_cursor = None
            self.log.debug("GhostCursor已禁用，将使用原生点击")
        self.log.success(f'new firefox browser success, global_timeout = {self.timeout}')
        return True

    def new_chrome_browser(self, proxy=None) -> bool:
        if proxy:
            if not check_ip(proxy['server'], log_instance=self.log):
                return False
        self.browser = self.playwright.chromium.launch(headless=False, proxy=proxy, timeout=self.timeout, slow_mo=643,
                                                    #    executable_path='C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe',
                                                        args=[
                                                            '--disable-blink-features=AutomationControlled'
                                                        ])
        # iphone_12 = self.playwright.devices['iPhone 12']
        # self.context = self.browser.new_context(**iphone_12, ignore_https_errors=True)
        self.context = self.browser.new_context(ignore_https_errors=True, has_touch=True)# viewport={'width': 1920, 'height': 1080})
        self.page = self.context.new_page()
        self.page_hook = self._new_page
        # 初始化GhostCursor（从config读取配置）
        ghost_cursor_enabled = getattr(config, 'ghost_cursor_enabled', True)
        if ghost_cursor_enabled:
            fast_mode = getattr(config, 'ghost_cursor_fast_mode', True)
            self.ghost_cursor = GhostCursor(self.page, self.log, fast_mode=fast_mode)
            self.log.debug(f"GhostCursor已启用，快速模式: {'启用' if fast_mode else '禁用'}")
        else:
            self.ghost_cursor = None
            self.log.debug("GhostCursor已禁用，将使用原生点击")
        self.log.success(f'new chrome browser success, global_timeout = {self.timeout}')
        return True
    
    def connect_browser(self, url) -> bool:
        self.browser = self.playwright.chromium.connect_over_cdp(url, timeout=self.timeout, slow_mo=916)
        if not self.browser.is_connected():
            self.log.err(f'[PlayWrightFrameWork.connect_browser] connect to browser failed, url = {url}')
            return False
        self.context = self.browser.contexts[0]
        self.page = self.context.pages[0]
        # self.browser.contexts[0].close()
        # self.context = self.browser.new_context(ignore_https_errors=True, has_touch=True)
        # self.context.clear_cookies()
        # self.page = self.context.new_page()
        self.page_hook = self._get_page
        self.log.success(f'connect to browser success, url = {url}')
        self.log.debug(self.cookie_to_str())
        return True


    def run(self, max_cnt=10, max_err=2) -> None:
        cnt = 0
        cnt_err = 0
        while cnt < max_cnt:
            if cnt_err >= max_err:
                break
            self.log.notice(f'>>>>>>>> cnt = {cnt} <<<<<<<<')
            self.worker()
            if not self.ok():
                cnt_err += 1
            else:
                self.cnt_ok += 1
                cnt_err = 0
            cnt += 1
            self.page.wait_for_timeout(2000)
            # self.clear_cookie_and_new_page()
    
    def add_cookies(self, cookies):
        self.context.add_cookies(cookies)
    
    def add_init_script(self, js:str | None=None, file=None):
        if file:
            self.context.add_init_script(path=file)
        else:
            self.context.add_init_script(script=js)
    
    def cookie_to_dict(self) -> dict:
        c = {}
        for item in self.context.cookies():
            # if item['domain'] == self.domain:
            c[item['name']] = item['value']
        return c

    def cookie_to_str(self) -> str:
        c = self.cookie_to_dict()
        s = ''
        for k, v in c.items():
            s += k + '=' + v + '; '
        return s[:-2]
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args) -> None:
        self.log.notice(f'COUNT = {self.cnt_ok}')
        self.cleanup()
        self.log.notice('exit.')
