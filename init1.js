//<script>
bouncex.init1Response({
    "brand_styles": {
        "31516": "\/* effects for .bx-campaign-31516 *\/\/* custom css .bx-campaign-31516 *\/\/************************************ BRAND STYLE STRUCTURE V4.0Do not remove or edit.************************************\/\/***LOGO***\/\/*Logo setting - MUST use img and not :first-child. <img> gets wrapped in <h2> in custom code *\/.bx-custom.bx-brand-31516 .bx-group .bx-row-image-logo > img, .bx-custom.bx-campaign-31516 .bx-group .bx-row-image-logo > img {    height: 40px;    width: auto;}\/* Loading Icon F (FLIP) *\/.bx-custom.bx-brand-31516 .bx-loader-flip > div,.bx-custom.bx-campaign-31516 .bx-loader-flip > div {    background-color: #e01a2b;    height: 30px;    width: 30px;    -webkit-animation: bx-anim-31516-loader-flip 1.2s infinite ease-in-out;    animation: bx-anim-31516-loader-flip 1.2s infinite ease-in-out;}@-webkit-keyframes bx-anim-31516-loader-flip {  0% { -webkit-transform: perspective(120px) }  50% { -webkit-transform: perspective(120px) rotateY(180deg) }  100% { -webkit-transform: perspective(120px) rotateY(180deg)  rotateX(180deg) }}@keyframes bx-anim-31516-loader-flip {  0% {     transform: perspective(120px) rotateX(0deg) rotateY(0deg);  }   50% {     transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);  }   100% {     transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);  }}\/***TIMER***\/\/*Units*\/.bx-custom.bx-brand-31516 .bx-timer-units, .bx-custom.bx-campaign-31516 .bx-timer-units {    min-width: 2.5em;    display:inline-block;    position: relative;}.bx-custom.bx-brand-31516 .bx-timer-units:after, .bx-custom.bx-campaign-31516 .bx-timer-units:after {    padding: 0 0 0 0.25em;}.bx-custom.bx-brand-31516 .bx-timer-units.bx-timer-days:after, .bx-custom.bx-campaign-31516 .bx-timer-units.bx-timer-days:after {  content: 'D';}.bx-custom.bx-brand-31516 .bx-timer-units.bx-timer-hours:after, .bx-custom.bx-campaign-31516 .bx-timer-units.bx-timer-hours:after {  content: 'H';}.bx-custom.bx-brand-31516 .bx-timer-units.bx-timer-minutes:after, .bx-custom.bx-campaign-31516 .bx-timer-units.bx-timer-minutes:after {  content: 'M';}.bx-custom.bx-brand-31516 .bx-timer-units.bx-timer-seconds:after, .bx-custom.bx-campaign-31516 .bx-timer-units.bx-timer-seconds:after {  content: 'S';}\/***CHECKBOX & RADIO***\/\/*Checkbox default*\/.bx-custom.bx-brand-31516 .bx-row-checkbox .bx-checkelem + .bx-checkshape,.bx-custom.bx-campaign-31516 .bx-row-checkbox .bx-checkelem + .bx-checkshape,.bx-custom.bx-brand-31516 .bx-row-radio .bx-radioelem + .bx-radioshape,.bx-custom.bx-campaign-31516 .bx-row-radio .bx-radioelem + .bx-radioshape {  flex: 0 0 auto;}.bx-custom.bx-brand-31516 .bx-row-checkbox .bx-label,.bx-custom.bx-campaign-31516 .bx-row-checkbox .bx-label,.bx-custom.bx-brand-31516 .bx-row-radio .bx-label,.bx-custom.bx-campaign-31516 .bx-row-radio .bx-label {    display: inline-flex;}\/***INPUT***\/\/*Shrink to top label fix*\/.bx-custom.bx-brand-31516 .bx-row-placeholder-top:not(.bx-row-input-label) input,.bx-custom.bx-campaign-31516 .bx-row-placeholder-top:not(.bx-row-input-label) input{    min-height: 2em !important;}\/*Persistent label*\/.bx-custom.bx-brand-31516 .bx-row-input.bx-row-input-label .bx-placeholder,.bx-custom.bx-campaign-31516 .bx-row-input.bx-row-input-label .bx-placeholder {    left: 0;    opacity: 1;    position: relative;}.bx-custom.bx-brand-31516 .bx-row-input.bx-row-input-label .bx-placeholder .bx-placeholdertext,.bx-custom.bx-campaign-31516 .bx-row-input.bx-row-input-label .bx-placeholder .bx-placeholdertext {    bottom: calc(100% + 1em);    color: #000000;    font-family: \"Macys Sans\", sans-serif;    font-size: 14px;    font-weight: 700;    left: 0;    position: absolute;    text-align: left;    text-transform: none;}\/***SELECT MENU***\/\/*Select (dropdown) arrows*\/.bx-custom.bx-brand-31516 .bx-row-select .bx-select:after,.bx-custom.bx-campaign-31516 .bx-row-select .bx-select:after {  color: inherit;}\/*** VALIDATION STYLES ***\/\/* Borders on input & select elements*\/.bx-custom.bx-brand-31516 .bx-row-input.bx-row-validation .bx-input,.bx-custom.bx-campaign-31516 .bx-row-input.bx-row-validation .bx-input,.bx-custom.bx-brand-31516 .bx-row-select.bx-row-validation .bx-select,.bx-custom.bx-campaign-31516 .bx-row-select.bx-row-validation .bx-select {    border-color: #ec0000; \/*Specify border color*\/}.bx-custom.bx-brand-31516 .bx-row-input.bx-row-validation .bx-input:focus,.bx-custom.bx-campaign-31516 .bx-row-input.bx-row-validation .bx-input:focus, .bx-custom.bx-brand-31516 .bx-row-select.bx-row-validation.bx-has-focus .bx-select,.bx-custom.bx-campaign-31516 .bx-row-select.bx-row-validation.bx-has-focus .bx-select {    border-color: #ec0000; \/*Specify border color on focus*\/}\/***FOCUS STYLES***\/\/* Note: These rules can be broken out to create separate focus sytles for different elementsEx: Text inputs could change background-color on focus, but radio buttons could recieve a styled outline*\/.bxc.bx-campaign-31516.bx-ally form:focus,.bxc.bx-campaign-31516.bx-ally a:focus,.bxc.bx-campaign-31516.bx-ally button:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-submit .bx-button:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=\"\"]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=email]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=password]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=tel]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=text]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=url]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=color]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=date]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=datetime]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=datetime-local]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=month]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=time]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=week]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=number]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-input [type=search]:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-textarea .bx-textarea:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-select .bx-selectelem:focus,.bxc.bx-campaign-31516.bx-ally .bx-row-checkbox .bx-checkelem:focus + .bx-checkshape,.bxc.bx-campaign-31516.bx-ally .bx-row-radio .bx-radioelem:focus + .bx-radioshape,.bxc.bx-brand-31516.bx-ally form:focus,.bxc.bx-brand-31516.bx-ally a:focus,.bxc.bx-brand-31516.bx-ally button:focus,.bxc.bx-brand-31516.bx-ally .bx-row-submit .bx-button:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=\"\"]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=email]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=password]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=tel]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=text]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=url]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=color]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=date]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=datetime]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=datetime-local]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=month]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=time]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=week]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=number]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-input [type=search]:focus,.bxc.bx-brand-31516.bx-ally .bx-row-textarea .bx-textarea:focus,.bxc.bx-brand-31516.bx-ally .bx-row-select .bx-selectelem:focus,.bxc.bx-brand-31516.bx-ally .bx-row-checkbox .bx-checkelem:focus + .bx-checkshape,.bxc.bx-brand-31516.bx-ally .bx-row-radio .bx-radioelem:focus + .bx-radioshape {    outline: 3px solid #025FCC;    box-shadow: 0 0 0px 6px #fff;    outline-offset: 0px;}\/***STRUCTURE***\/\/*General*\/.bx-custom.bx-brand-31516 .bx-row,.bx-custom.bx-brand-31516 .bx-group, .bx-custom.bx-campaign-31516 .bx-row,.bx-custom.bx-campaign-31516 .bx-group {  max-width: 100%;}.bx-custom.bx-brand-31516 .bx-row > a,.bx-custom.bx-campaign-31516 .bx-row > a {   display: block;}.bx-custom.bx-brand-31516 .bx-row-image, .bx-custom.bx-campaign-31516 .bx-row-image,.bx-custom.bx-brand-31516 .bx-row-line-logo, .bx-custom.bx-campaign-31516 .bx-row-line-logo {  padding: 0;}\/***VX ADDITIONS - COPY PASTE TO OLD BRAND STYLE***\/\/*Firefox fix*\/.bx-custom.bx-brand-31516 .bx-el::-moz-placeholder,.bx-custom.bx-campaign-31516 .bx-el::-moz-placeholder {    opacity: 1;}\/*Font smoothing fix*\/.bx-custom.bx-brand-31516 .bx-creative,.bx-custom.bx-campaign-31516 .bx-creative {  -webkit-font-smoothing: auto;  -moz-osx-font-smoothing: auto;  -moz-text-size-adjust: none;}\/*Lingering buttons fix*\/.bx-custom.bx-brand-31516 a,.bx-custom.bx-campaign-31516 a {    transition: none;}\/* Prevent links becoming large when text is small on phones*\/.bx-custom.bx-brand-31516 .bx-row *,.bx-custom.bx-campaign-31516  .bx-row * {    -webkit-text-size-adjust: 100%;}\/* Focus outline adjustment *\/.bxc.bx-custom.bx-brand-31516 .bx-2-heading,.bxc.bx-custom.bx-campaign-31516 .bx-2-heading {    display: inline-block;    max-width: 100%;}.bxc.bx-custom.bx-brand-31516 .bx-2-heading[tabindex=\"-1\"]:focus,.bxc.bx-custom.bx-campaign-31516 .bx-2-heading[tabindex=\"-1\"]:focus {    outline: 3px solid transparent;}.bxc.bx-brand-31516 .bx-creative> *:first-child {width: 400px;}.bxc.bx-brand-31516 .bx-creative:before {min-height: 480px;}.bxc.bx-brand-31516 .bx-shroud {background-size: cover;}.bxc.bx-brand-31516 .bx-close {width: 48px;top: 0;right: 0;height: 48px;padding: 12px;border-radius: 0;stroke-width: 1px;stroke: black;}.bxc.bx-brand-31516 .bx-row-image-logo {padding: 0;width: auto;}.bxc.bx-brand-31516 .bx-row-image-logo> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-brand-31516 .bx-row-html-default {width: auto;text-align: center;}.bxc.bx-brand-31516 .bx-row-text-custom> *:first-child {font-size: 25px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-text-custom {padding: 0;}.bxc.bx-brand-31516 .bx-row-text-subheadline> *:first-child {font-size: 14px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 500;line-height: 1.43em;color: black;}.bxc.bx-brand-31516 .bx-row-text-subheadline {padding: 0;}.bxc.bx-brand-31516 .bx-row-text-headline> *:first-child {font-size: 36px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: black;line-height: 1.1;}.bxc.bx-brand-31516 .bx-row-text-headline {padding: 0;}.bxc.bx-brand-31516 .bx-row-text-default> *:first-child {font-size: 14px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;line-height: 1.43em;color: black;}.bxc.bx-brand-31516 .bx-row-text-default {padding: 0;}.bxc.bx-brand-31516 .bx-row-timer-default {width: auto;padding: 0;}.bxc.bx-brand-31516 .bx-row-timer-default> *:first-child {padding: 0;border-style: none;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: black;}.bxc.bx-brand-31516 .bx-row-coupon-default {width: auto;padding: 0;}.bxc.bx-brand-31516 .bx-row-coupon-default> *:first-child {padding: 0;border-style: solid;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: black;}.bxc.bx-brand-31516 .bx-row-checkbox-GDPR {padding: 0;text-align: left;}.bxc.bx-brand-31516 .bx-row-checkbox-GDPR> *:first-child {line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;color: black;}.bxc.bx-brand-31516 .bx-row-checkbox-GDPR .bx-checkelem+.bx-checkshape.bx-component {background-color: transparent;width: 18px;height: 18px;margin-right: 8px;border-radius: 0;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31516 .bx-row-checkbox-GDPR .bx-checkelem:checked+.bx-checkshape.bx-component.bx-component {background-color: transparent;box-shadow: none;stroke: #000000;}.bxc.bx-brand-31516 .bx-row-checkbox-GDPR .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-checkbox-GDPR .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-checkbox-default {padding: 0;text-align: left;}.bxc.bx-brand-31516 .bx-row-checkbox-default> *:first-child {line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;color: black;}.bxc.bx-brand-31516 .bx-row-checkbox-default .bx-checkelem+.bx-checkshape.bx-component {background-color: transparent;width: 18px;height: 18px;margin-right: 8px;border-radius: 0;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31516 .bx-row-checkbox-default .bx-checkelem:checked+.bx-checkshape.bx-component.bx-component {background-color: transparent;box-shadow: none;stroke: #000000;}.bxc.bx-brand-31516 .bx-row-checkbox-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-checkbox-default .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-radio-default {padding: 0;text-align: left;}.bxc.bx-brand-31516 .bx-row-radio-default> *:first-child {padding: 0;line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-radio-default .bx-radioelem+.bx-radioshape.bx-component {background-color: transparent;width: 18px;height: 18px;margin-right: 8px;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31516 .bx-row-radio-default .bx-radioelem:checked+.bx-radioshape.bx-component.bx-component {background-color: transparent;box-shadow: none;fill: #000000;}.bxc.bx-brand-31516 .bx-row-radio-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-radio-default .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-input-default .bx-el {font-size: 16px;text-align: left;background-color: transparent;border-style: solid;border-color: #c0c0c0;border-width: 1px;padding: 11.5px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-input-default .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-brand-31516 .bx-row-input-default .bx-el::-webkit-input-placeholder {color: rgb(0, 0, 0);}.bxc.bx-brand-31516 .bx-row-input-default .bx-el:-moz-placeholder {color: rgb(0, 0, 0);}.bxc.bx-brand-31516 .bx-row-input-default .bx-el::-moz-placeholder {color: rgb(0, 0, 0);}.bxc.bx-brand-31516 .bx-row-input-default .bx-el:-ms-input-placeholder {color: rgb(0, 0, 0);}.bxc.bx-brand-31516 .bx-row-input-default .bx-el {padding: 11.5px;}.bxc.bx-brand-31516 .bx-row-input-default {padding: 5px;text-align: left;}.bxc.bx-brand-31516 .bx-row-input-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-input-default .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el {padding: 11.5px;font-size: 16px;text-align: left;background-color: transparent;border-style: solid;border-color: #c0c0c0;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el::-webkit-input-placeholder {color: transparent;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el:-moz-placeholder {color: transparent;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el::-moz-placeholder {color: transparent;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el:-ms-input-placeholder {color: transparent;}.bxc.bx-brand-31516 .bx-row-input-label .bx-el {padding: 11.5px;}.bxc.bx-brand-31516 .bx-row-input-label {padding: 15px 5px 5px 5px;}.bxc.bx-brand-31516 .bx-row-input-label .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-input-label .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-select-default {padding: 5px;}.bxc.bx-brand-31516 .bx-row-select-default> *:first-child {padding: 12.5px;font-size: 16px;line-height: 1.2;border-color: #c0c0c0;background-color: transparent;color: #000000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-select-default> *:first-child:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-brand-31516 .bx-row-select-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31516 .bx-has-focus.bx-row-select-default .bx-component-validation {color: #ec0000;}.bxc.bx-brand-31516 .bx-row-submit-default> *:first-child {padding: 18px;font-size: 14px;background-color: black;border-style: solid;border-color: black;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;border-radius: 5px;}.bxc.bx-brand-31516 .bx-row-submit-default {padding: 5px;}.bxc.bx-brand-31516 .bx-row-submit-custom> *:first-child {background-color: transparent;padding: 18px;font-size: 14px;color: black;border-style: solid;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;border-radius: 5px;}.bxc.bx-brand-31516 .bx-row-submit-custom {padding: 5px;}.bxc.bx-brand-31516 .bx-row-submit-no> *:first-child {background-color: transparent;padding: 0;font-size: 14px;color: black;text-decoration: underline;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;}.bxc.bx-brand-31516 .bx-row-submit-no {padding: 0;width: auto;}.bxc.bx-brand-31516 .bx-row-text-link {padding: 0;width: auto;}.bxc.bx-brand-31516 .bx-row-text-link> *:first-child {padding: 0;font-size: 12px;font-weight: 400;cursor: pointer;text-decoration: underline;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;}.bxc.bx-brand-31516 .bx-row-text-link> *:first-child:hover {-webkit-transition: color .15s ease-in-out;transition: color .15s ease-in-out;}.bxc.bx-brand-31516 .bx-row-text-sosumi {width: auto;padding: 0;}.bxc.bx-brand-31516 .bx-row-text-sosumi> *:first-child {padding: 0;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;}"
    },
    "webfonts": false,
    "cookie": {
        "swv": false,
        "did": "8945402996039390164",
        "vid": 1741336285707714,
        "v": {
            "ever_logged_in": false,
            "cart_items": "false",
            "cart": false,
            "cart_items_qty": "false",
            "cart_items_offset": "false",
            "cart_set": "false",
            "logged_in_identified": "false",
            "welcome_code": false,
            "starrewards_login_ever": false,
            "quickreg_submit": "false",
            "quickreg_submit_ever": false,
            "is_subscribed": false,
            "coupon_code_url": "false",
            "sfl_item_ids": false
        },
        "pdFirstLoad": true,
        "dg": {
            "isPreviousCustomer": false,
            "isSubscriber": false,
            "isTextSubscriber": false,
            "cache_ts": 1741336283850
        },
        "fvt": 1741336285,
        "ao": 0,
        "lp": "https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fmacys-deals-and-coupons%3Fid%3D334356",
        "as": 0,
        "vpv": 1,
        "d": "d",
        "r": "",
        "cvt": 1741336285,
        "sid": -1,
        "gcr": 34,
        "m": 0,
        "lvt": 1741336285,
        "campaigns": {
            "2504550": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2504554": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2534932": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2534938": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2842942": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2846223": {
                "vv": 0,
                "lvt": 1741336285
            },
            "2846225": {
                "vv": 0,
                "lvt": 1741336285
            }
        }
    },
    "debug": false,
    "testmode": {
        "bxtest": false,
        "office": false,
        "bxdev": false
    },
    "state": {
        "newvid": true,
        "iol": true,
        "tn": 1741336285,
        "pvid": 1,
        "gdpr": false,
        "casl": false,
        "us": true,
        "device": {
            "browser": "Chrome",
            "version": "*********",
            "platform": "Windows 10",
            "device_type": "desktop"
        },
        "no_kinesis": true,
        "request_token": "9efc2bc1287f7403bb76eb589a120e8e6b3bf584c93d06a19e2a58e90a786098",
        "mobile": false,
        "vip": "***************",
        "geo": {
            "country": "US",
            "country_code": "US",
            "country_code3": "USA",
            "country_name": "United States",
            "region": "CA",
            "region_name": "California",
            "city": "San Jose",
            "postal_code": "95119",
            "zip": "95119",
            "latitude": 37.200000000000003,
            "longitude": -121.8,
            "area_code": "",
            "dma_code": 807,
            "metro_code": 807,
            "continent_code": "NA"
        }
    },
    "gbi_stacks": false,
    "creatives": false,
    "campaigns": {
        "2504550": {
            "ad_shown": false,
            "pid": "2504549",
            "pname": "Email + Text | Persistent | Part 1\/4 | Net-New | All Devices | Phase 2.0",
            "purpose": "Email Capture",
            "purpose_code": "emailcapture",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "emailcapture-entrance2",
            "name": "WKND | Persistent | Treatment | Email + Text Capture",
            "activations": [{
                "activation": "timer",
                "val": "0"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 0,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "header.desktop-redesign #link-rail div.rail-block, header:not(.desktop-redesign):not(.desktop), header.desktop .link-rail .links-wrap li",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "prepend",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "reload",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 0,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "agilityzone",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "no_effect",
            "te2": "no_effect",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element: 'wunderkind spring 2024 sms and email sign up step1',\n    zone_type : 'sms and mailsignup',\n    attribute2: ca_id,\n    media_type:'sms and email entrance lightbox'\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'click' : {\n        event_name : 'content link click',\n        link_name : 'wunderkind sms and email light box_2504550_step1 - clicks to sign up'\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "var $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n}); bouncex.addCss('body[style*=\"top: 0px; position: fixed; width: 100%; overflow: hidden;\"] header:not(.left-nav-visible) #bx-campaign-'+campaign_id+' .bx-slab { display: none; } @media screen and (min-width: 1024px) { header.desktop-redesign #link-rail div.rail-block #media-rail, header.desktop .link-rail .links-wrap li .media-rail { display: none !important; }} @media screen and (max-width: 1023px) { header #redesign-media-rail .redesign-header-wrapper .redesign-header-media-content, #bx-campaign-'+campaign_id+' + .media-rail.text-center { display: none !important; } header[data-uri].compact.compact-redesign.responsive .redesign-header-media .redesign-header-wrapper { height: auto; } #bx-campaign-'+campaign_id+' .bx-wrap { width: auto; }}', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-hideNativeAd');\n\nfunction updateCampaignPlacement() {\n    var screenWidth = bouncex.wndsize().width;\n     \n    if (screenWidth >= 1024) {\n        bouncex.campaigns[campaign_id].callout_t = 'header.desktop-redesign #link-rail div.rail-block, header.desktop .link-rail .links-wrap li';\n    } else {\n        bouncex.campaigns[campaign_id].callout_t = 'header:not(.desktop-redesign):not(.desktop):not(.slideout-header)';\n    }\n    \n     \n    bouncex.campaigns[campaign_id].calloutTarget = jQuery(bouncex.campaigns[campaign_id].callout_t).eq(0);\n    bouncex.placeCampaign(campaign_id);\n    bouncex.alignCampaign(campaign_id);\n}\n\nvar bxResizeTimeout,\n    newWidth,\n    oldWidth = bouncex.window.width();\n\nbouncex.on(bouncex.window, 'resize.bx-'+ca_id, function(){\n    window.clearTimeout(bxResizeTimeout);\n    bxResizeTimeout = bouncex.setTimeout2(function() {\n        updateCampaignPlacement();\n    }, 250);\n    \n    newWidth = bouncex.window.width();\n    if ((newWidth > 1023 && oldWidth < 1023) || (oldWidth > 1023 && newWidth < 1023)) {\n        bouncex.close_ad(ca_id);\n    }\n});\n\nupdateCampaignPlacement();\n\n\/* custom utag impression - nav inline zone - PAUSED 10\/2\/23 *\/\n\/\/ bouncex.utagReport({eventType: 'impression'}, {\n\/\/     zone_type: 'emailsignup popup'\n\/\/ });",
                "click": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.click', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'click',\n    customProps : customProps\n}, impressionData);",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-hideNativeAd-style').remove();\nbouncex.off(bouncex.window, 'resize.bx-'+campaign_id);"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "Email Capture - Entrance - Email + Text | Persistent | Part 1\/4 | Net-New | All Devices | Phase 2.0 (2504549): Inline Zone - variation - WKND | Persistent | Treatment | Email + Text Capture (2504550)"
            },
            "is_bpush": false,
            "z_index": null,
            "push_optin_json": null,
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "subtype": "",
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 1,
            "closePlacement": [false],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2504550  bx-brand-31516 bx-width-default bx-type-agilityzone  bx-has-close-x-1 \" id=\"bx-campaign-2504550\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2504550\"><div id=\"bx-shroud-2504550\" class=\"bx-matte bx-shroud bx-shroud-2504550\"><\/div><div id=\"bx-hover-shroud-2504550\" class=\"bx-hover-shroud bx-hover-shroud-2504550\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2504550\" id=\"bx-creative-2504550\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2504550\" class=\"bx-ally-title\">Sign up for emails & get an extra 25% off.<\/div><button id=\"bx-close-inside-2504550\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up banner<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-YlKCcfO bx-step-2504550-1 bx-tail-placement-hidden\" id=\"bx-step-2504550-1\" data-close-placement=\"\"><form id=\"bx-form-2504550-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2504550); return false\" onreset=\"bouncex.close_ad(2504550); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2504550\"><input type=\"hidden\" name=\"campaign_id\" value=\"2504550\" \/><div class=\"bx-group bx-group-default bx-group-2504550-FubbP2A bx-group-FubbP2A\" id=\"bx-group-2504550-FubbP2A\"  ><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-kKol1UI bx-element-2504550-kKol1UI\" id=\"bx-element-2504550-kKol1UI\"  ><div>Sign up for emails & get an extra 25% off!<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-custom  bx-row-DNRIrqS bx-element-2504550-DNRIrqS\" id=\"bx-element-2504550-DNRIrqS\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >Exclusions apply.<\/a><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-custom  bx-row-bWMdcYA bx-element-2504550-bWMdcYA\" id=\"bx-element-2504550-bWMdcYA\"  ><a href=\"javascript:void(0)\" target=\"\" class=\"bx-button\" data-click=\"trigger\" data-click-trigger=\"2504553\" >Sign Up<\/a><\/a><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2504550\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up banner<\/span><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2504550 *\/\/* custom css .bx-campaign-2504550 *\/\/* custom css from creative 83328 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2504550 *\/.bxc.bx-campaign-2504550.bx-active-step-1 .bx-creative:before {min-height: 43px;}.bxc.bx-campaign-2504550.bx-active-step-1 .bx-creative> *:first-child {width: 500px;}@media all and (max-width: 736px) {.bxc.bx-campaign-2504550.bx-active-step-1 .bx-creative> *:first-child {width: 350px;padding: 1px;}}.bxc.bx-campaign-2504550 .bx-group-2504550-FubbP2A {text-align: left;}@media all and (max-width: 736px) {.bxc.bx-campaign-2504550 .bx-group-2504550-FubbP2A {text-align: center;}}.bxc.bx-campaign-2504550 .bx-element-2504550-kKol1UI {width: auto;}@media all and (max-width: 736px) {.bxc.bx-campaign-2504550 .bx-element-2504550-kKol1UI {padding: 5px 0;}}.bxc.bx-campaign-2504550 .bx-element-2504550-kKol1UI> *:first-child {font-size: 12px;line-height: 12px;}.bxc.bx-campaign-2504550 .bx-element-2504550-DNRIrqS> *:first-child {text-decoration: none;border-style: none;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;font-size: 12px;line-height: 12px;}.bxc.bx-campaign-2504550 .bx-element-2504550-DNRIrqS {width: auto;}.bxc.bx-campaign-2504550 .bx-element-2504550-bWMdcYA> *:first-child {text-decoration: underline;border-style: none;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;font-size: 12px;line-height: 12px;}.bxc.bx-campaign-2504550 .bx-element-2504550-bWMdcYA {width: auto;padding: 0;}@media all and (max-width: 736px) {.bxc.bx-campaign-2504550 .bx-element-2504550-bWMdcYA {width: 90%;padding: 1px;}}"
        },
        "2504554": {
            "ad_shown": false,
            "pid": "2504553",
            "pname": "Email + Text | Persistent | Part 2\/4 | Net-New | All Devices | Email Capture Step",
            "purpose": "Email Capture",
            "purpose_code": "emailcapture",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "emailcapture-entrance2",
            "name": "WKND | Persistent | Net-New | Overlay",
            "activations": [{
                "activation": "manual"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 1,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 1,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element:'wunderkind spring 2024 sms and email sign up step2',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind sms and email lightbox impression',\n        link_name: 'wunderkind spring 2024 sms and email sign up step2_2504554_impression'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        site_action : 'email signup',\n        action_location: 'wunderkind spring 2024 sms and email sign up',\n        link_name: 'wunderkind spring 2024 sms and email sign up step2_2504554_get 25% off'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind sms and email entrance lightbox 2024_step2',\n        error_code : ca_id,\n        error_label : \"Email Address\",\n        error_message : '', \/\/ DYNAMIC \"Invalid Email Address\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    bouncex.log('validateform', bouncex.utils.getNestedProp('response.errors.email', '', e));\n    if (bouncex.utils.getNestedProp('response.errors.email', '', e).indexOf('already been subscribed') > -1){\n        bouncex.close_ad(ca_id);\n    }\n}); ",
                "click": "",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();if (bouncex.cookie.campaigns[ca_id].ls) {\n    bouncex.show_ad(2534932);\n} else if (bouncex.cookie.es) {\n    bouncex.show_ad(2534938);\n}"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "Email Capture - Entrance - Email + Text | Persistent | Part 2\/4 | Net-New | All Devices | Email Capture Step (2504553): Overlay - variation - WKND | Persistent | Net-New | Overlay (2504554)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": null,
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 1,
            "numSteps": 1,
            "closePlacement": ["inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2504554  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2504554\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2504554\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2504554\" class=\"bx-matte bx-shroud bx-shroud-2504554\"><\/div><div id=\"bx-hover-shroud-2504554\" class=\"bx-hover-shroud bx-hover-shroud-2504554\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2504554\" id=\"bx-creative-2504554\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2504554\" class=\"bx-ally-title\">Enter your email for 25% off your first order!<\/div><button id=\"bx-close-inside-2504554\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-rFM6eW2 bx-step-2504554-1 bx-tail-placement-hidden\" id=\"bx-step-2504554-1\" data-close-placement=\"inside\"><form id=\"bx-form-2504554-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2504554); return false\" onreset=\"bouncex.close_ad(2504554); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2504554\"><input type=\"hidden\" name=\"campaign_id\" value=\"2504554\" \/><div class=\"bx-group bx-group-primary bx-group-2504554-sLB1iY3 bx-group-sLB1iY3\" id=\"bx-group-2504554-sLB1iY3\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-sEmLxRy bx-element-2504554-sEmLxRy\" id=\"bx-element-2504554-sEmLxRy\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2504554-wMtK9lt bx-group-wMtK9lt\" id=\"bx-group-2504554-wMtK9lt\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-7Q02G7Q bx-element-2504554-7Q02G7Q\" id=\"bx-element-2504554-7Q02G7Q\"  ><div>Unlock 25% off*<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-e0clKf2 bx-element-2504554-e0clKf2\" id=\"bx-element-2504554-e0clKf2\"  ><div>your first order when you join our list.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2504554-sqEkLIh bx-group-sqEkLIh\" id=\"bx-group-2504554-sqEkLIh\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-aiDVbjg bx-element-2504554-aiDVbjg\" id=\"bx-element-2504554-aiDVbjg\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2504554-aiDVbjg-input\" class=\"bx-component bx-component-hN4P652 bx-component-2504554-hN4P652 bx-placeholder bx-component-placeholder\" id=\"bx-component-2504554-hN4P652\"><span class=\"bx-placeholdertext\">Your Email Here<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2504554-aiDVbjg-input\" type=\"email\" name=\"email\" placeholder=\"Your Email Here\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-NbbQijc bx-component-2504554-NbbQijc bx-component-validation bx-vtext bx-error-2504554-email\" id=\"bx-error-2504554-email\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-GmylQGJ bx-element-2504554-GmylQGJ\" id=\"bx-element-2504554-GmylQGJ\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Get 25% Off<\/button><\/a><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-p83Rzy0 bx-element-2504554-p83Rzy0\" id=\"bx-element-2504554-p83Rzy0\"  ><div>By continuing, you agree to Macy's <a href=\"https:\/\/customerservice-macys.com\/articles\/macys-and-macyscom-notice-of-privacy-practices-2#collect.info\" style=\"text-decoration: underline;\" target=\"_blank\">Privacy Practices<\/a>. <\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-Fq1JsIn bx-element-2504554-Fq1JsIn\" id=\"bx-element-2504554-Fq1JsIn\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >*Offer exclusions apply<\/a><\/a><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2504554-VdXcRql bx-group-VdXcRql\" id=\"bx-group-2504554-VdXcRql\"  ><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-rUPMwaw bx-element-2504554-rUPMwaw\" id=\"bx-element-2504554-rUPMwaw\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2504554\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2504554 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2504554 *\/\/* custom css from creative 70947 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2504554 *\/.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2504554.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2504554 .bx-group-2504554-sLB1iY3 {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554 .bx-group-2504554-sLB1iY3 {display: none;}}.bxc.bx-campaign-2504554 .bx-element-2504554-sEmLxRy {padding: 0;width: auto;}.bxc.bx-campaign-2504554 .bx-element-2504554-sEmLxRy> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2504554 .bx-group-2504554-wMtK9lt {width: 300px;padding: 0 0 35px;}.bxc.bx-campaign-2504554 .bx-element-2504554-7Q02G7Q {padding: 0 0 15px;}.bxc.bx-campaign-2504554 .bx-group-2504554-sqEkLIh {width: 90%;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554 .bx-group-2504554-sqEkLIh {width: 450px;}}.bxc.bx-campaign-2504554 .bx-element-2504554-aiDVbjg .bx-el {padding: 11.5px;}.bxc.bx-campaign-2504554 .bx-element-2504554-aiDVbjg .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2504554 .bx-has-text.bx-element-2504554-aiDVbjg .bx-el {padding: 11.5px;}.bxc.bx-campaign-2504554 .bx-element-2504554-aiDVbjg {width: 290px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554 .bx-element-2504554-aiDVbjg {width: 100%;}}.bxc.bx-campaign-2504554 .bx-row-validation .bx-component-2504554-NbbQijc {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2504554 .bx-has-focus.bx-row-validation .bx-component-2504554-NbbQijc {color: #ec0000;}.bxc.bx-campaign-2504554 .bx-element-2504554-GmylQGJ {width: 290px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554 .bx-element-2504554-GmylQGJ {width: 100%;}}.bxc.bx-campaign-2504554 .bx-element-2504554-p83Rzy0 {width: 90%;padding: 10px 0 0;}.bxc.bx-campaign-2504554 .bx-element-2504554-p83Rzy0> *:first-child {font-style: italic;font-size: 12px;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2504554 .bx-element-2504554-p83Rzy0> *:first-child {font-size: 10px;}}.bxc.bx-campaign-2504554 .bx-element-2504554-Fq1JsIn {width: auto;padding: 4px 0 0;}.bxc.bx-campaign-2504554 .bx-element-2504554-Fq1JsIn> *:first-child {font-style: italic;font-size: 12px;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2504554 .bx-element-2504554-Fq1JsIn> *:first-child {font-size: 10px;}}.bxc.bx-campaign-2504554 .bx-group-2504554-VdXcRql {padding: 30px 0 0;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2504554 .bx-group-2504554-VdXcRql {padding: 15px 0 0;}}.bxc.bx-campaign-2504554 .bx-element-2504554-rUPMwaw {padding: 22px 0 0;}"
        },
        "2534932": {
            "ad_shown": false,
            "pid": "2534931",
            "pname": "Email + Text | Persistent | Part 3\/4 | Net-New | All Devices | Net-New Email Text Capture",
            "purpose": "SMS Opt-In",
            "purpose_code": "smsoptin",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "smsoptin-entrance1",
            "name": "WKND | Persistent | Net-New | Overlay",
            "activations": [{
                "activation": "manual"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 1,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "reload",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 1,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg", "assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element: 'wunderkind spring 2024 sms and email sign up step3a',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id,\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind sms and email light box step3a impression',\n        link_name: 'wunderkind sms and email light box step3a impression_ 2534932_impression'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        link_name: 'wunderkind sms and email light box step3a_2534932 - clicks to sign up for texts',\n        site_action : 'text signup',\n        action_location: 'wunderkind spring 2024 sms and email sign up'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind sms and email entrance lightbox 2024_step3a',\n        error_code : ca_id,\n        error_label : \"Phone number\",\n        error_message : '', \/\/ DYNAMIC \"Invalid phone number\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);var newUser = 'AFJ0PlW',\n    duplicateUser = 'N1CDJuJ';\n\n\/* ---- EDIT ABOVE THIS LINE --- \"continue\" event *\/\nvar $dupeContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + duplicateUser + ' .bx-button'),\n    $newUserContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + newUser + ' .bx-button');\n\nbouncex.on($dupeContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType: 'continue',\n        customProps: {\n            event_name : 'wunderkind sms and email entrance lightbox 2024_step4b_2534932_you are already on the list_continue shopping',\n            custom_link_name : 'wunderkind sms and email entrance lightbox 2024_step4b_2534932_you are already on the list_continue shopping'\n        }\n    }); \/\/ default event uses \"already\" language\n    bouncex.off($dupeContinueButton, 'click.wknd' + ca_id);\n});\n\nbouncex.on($newUserContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType : 'continue',\n        customProps: {\n            event_name : 'wunderkind sms and email entrance lightbox 2024_step4a_2534932_you are on the list_continue shopping',\n            custom_link_name : 'wunderkind sms and email entrance lightbox 2024_step4a_2534932_you are on the list_continue shopping'\n        }\n    });\n    bouncex.off($newUserContinueButton, 'click.wknd' + ca_id);\n});\n\n\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e).indexOf('already been subscribed') > -1){\n        bouncex.setJumpStep(ca_id, 3);\n        bouncex.nextStep(ca_id);\n    }\n}); \n\n\/* Update SMS error message after 3 tries *\/\nvar errorCount = 0;\n\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n        errorCount++;\n        \n        if (errorCount > 2) {\n            jQuery('#bx-error-' + ca_id + '-phone_number').text('Please submit a different phone number');\n        }\n    }\n}); ",
                "click": "",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "SMS Opt-In - Entrance - Email + Text | Persistent | Part 3\/4 | Net-New | All Devices | Net-New Email Text Capture (2534931): Overlay - variation - WKND | Persistent | Net-New | Overlay (2534932)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": "",
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 3,
            "closePlacement": ["inside", "inside", "inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2534932  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2534932\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2534932\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2534932\" class=\"bx-matte bx-shroud bx-shroud-2534932\"><\/div><div id=\"bx-hover-shroud-2534932\" class=\"bx-hover-shroud bx-hover-shroud-2534932\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2534932\" id=\"bx-creative-2534932\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2534932\" class=\"bx-ally-title\">Enter your phone number and sign up for texts too!<\/div><button id=\"bx-close-inside-2534932\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-4Vp3zd8 bx-step-2534932-1 bx-tail-placement-hidden\" id=\"bx-step-2534932-1\" data-close-placement=\"inside\"><form id=\"bx-form-2534932-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534932); return false\" onreset=\"bouncex.close_ad(2534932); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534932\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534932\" \/><div class=\"bx-group bx-group-primary bx-group-2534932-qHwrmeS bx-group-qHwrmeS\" id=\"bx-group-2534932-qHwrmeS\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kyKuYL1 bx-element-2534932-kyKuYL1\" id=\"bx-element-2534932-kyKuYL1\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-fshJyx6 bx-group-fshJyx6\" id=\"bx-group-2534932-fshJyx6\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-EKDXzpR bx-element-2534932-EKDXzpR\" id=\"bx-element-2534932-EKDXzpR\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-uM5i8iz bx-element-2534932-uM5i8iz\" id=\"bx-element-2534932-uM5i8iz\"  ><div>Sign up for texts too.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-csrCyXo bx-group-csrCyXo\" id=\"bx-group-2534932-csrCyXo\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-WefZKz0 bx-element-2534932-WefZKz0\" id=\"bx-element-2534932-WefZKz0\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2534932-WefZKz0-input\" class=\"bx-component bx-component-STiQRuB bx-component-2534932-STiQRuB bx-placeholder bx-component-placeholder\" id=\"bx-component-2534932-STiQRuB\"><span class=\"bx-placeholdertext\">Phone Number<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2534932-WefZKz0-input\" type=\"tel\" name=\"phone_number\" placeholder=\"Phone Number\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-P0M97kU bx-component-2534932-P0M97kU bx-component-validation bx-vtext bx-error-2534932-phone_number\" id=\"bx-error-2534932-phone_number\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-sosumi  bx-row-u0DHaTO bx-element-2534932-u0DHaTO\" id=\"bx-element-2534932-u0DHaTO\"  ><div>By entering your number, you consent to receive recurring autodialed marketing msgs to your mobile device. Consent not required to buy. Msg&data rates may apply. Customer Service: ************<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-Ivo4o7U bx-element-2534932-Ivo4o7U\" id=\"bx-element-2534932-Ivo4o7U\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Sign Up for Texts <br> <span style=\"font-size:10px;\"><\/span><\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-PKwLHFJ bx-element-2534932-PKwLHFJ\" id=\"bx-element-2534932-PKwLHFJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><div class=\"bx-step bx-step-2  bx-step-FeeCY1E bx-step-2534932-2 bx-tail-placement-hidden\" id=\"bx-step-2534932-2\" data-close-placement=\"inside\"><form id=\"bx-form-2534932-step-2\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534932); return false\" onreset=\"bouncex.close_ad(2534932); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534932\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534932\" \/><div class=\"bx-group bx-group-primary bx-group-2534932-BO8GwHi bx-group-BO8GwHi\" id=\"bx-group-2534932-BO8GwHi\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kURMwTG bx-element-2534932-kURMwTG\" id=\"bx-element-2534932-kURMwTG\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-b7aWr3P bx-group-b7aWr3P\" id=\"bx-group-2534932-b7aWr3P\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-1S8R9dZ bx-element-2534932-1S8R9dZ\" id=\"bx-element-2534932-1S8R9dZ\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-aXqgZ8K bx-element-2534932-aXqgZ8K\" id=\"bx-element-2534932-aXqgZ8K\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-5b6qLna bx-element-2534932-5b6qLna\" id=\"bx-element-2534932-5b6qLna\"  ><div>Check your texts for next steps.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-SVZLgNI bx-group-SVZLgNI\" id=\"bx-group-2534932-SVZLgNI\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-AFJ0PlW bx-element-2534932-AFJ0PlW\" id=\"bx-element-2534932-AFJ0PlW\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><div class=\"bx-step bx-step-3  bx-step-NLjKFpi bx-step-2534932-3 bx-tail-placement-hidden\" id=\"bx-step-2534932-3\" data-close-placement=\"inside\"><form id=\"bx-form-2534932-step-3\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534932); return false\" onreset=\"bouncex.close_ad(2534932); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534932\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534932\" \/><div class=\"bx-group bx-group-primary bx-group-2534932-56szcKo bx-group-56szcKo\" id=\"bx-group-2534932-56szcKo\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-z1GoOlV bx-element-2534932-z1GoOlV\" id=\"bx-element-2534932-z1GoOlV\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-g3NYL7l bx-group-g3NYL7l\" id=\"bx-group-2534932-g3NYL7l\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-iLLRYfa bx-element-2534932-iLLRYfa\" id=\"bx-element-2534932-iLLRYfa\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-gH4iCNS bx-element-2534932-gH4iCNS\" id=\"bx-element-2534932-gH4iCNS\"  ><div>You're already on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-qOr40Pv bx-element-2534932-qOr40Pv\" id=\"bx-element-2534932-qOr40Pv\"  ><div>You'll still enjoy Macy's perks.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534932-gy42ESn bx-group-gy42ESn\" id=\"bx-group-2534932-gy42ESn\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-N1CDJuJ bx-element-2534932-N1CDJuJ\" id=\"bx-element-2534932-N1CDJuJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2534932\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2534932 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2534932 *\/\/* custom css from creative 91474 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2534932 *\/.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534932.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534932 .bx-group-2534932-qHwrmeS {padding: 0 0 25px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932 .bx-group-2534932-qHwrmeS {display: none;}}.bxc.bx-campaign-2534932 .bx-element-2534932-kyKuYL1 {padding: 0;width: auto;}.bxc.bx-campaign-2534932 .bx-element-2534932-kyKuYL1> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534932 .bx-group-2534932-fshJyx6 {width: 320px;padding: 0 0 35px;}.bxc.bx-campaign-2534932 .bx-element-2534932-EKDXzpR {padding: 0 0 15px;}.bxc.bx-campaign-2534932 .bx-group-2534932-csrCyXo {width: 280px;}.bxc.bx-campaign-2534932 .bx-element-2534932-WefZKz0 .bx-el {padding: 11.5px;}.bxc.bx-campaign-2534932 .bx-element-2534932-WefZKz0 .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2534932 .bx-has-text.bx-element-2534932-WefZKz0 .bx-el {padding: 11.5px;}.bxc.bx-campaign-2534932 .bx-row-validation .bx-component-2534932-P0M97kU {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2534932 .bx-has-focus.bx-row-validation .bx-component-2534932-P0M97kU {color: #ec0000;}.bxc.bx-campaign-2534932 .bx-element-2534932-u0DHaTO {width: auto;padding: 0;}.bxc.bx-campaign-2534932 .bx-element-2534932-u0DHaTO> *:first-child {padding: 10px 5px ;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;line-height: 1.2em;}.bxc.bx-campaign-2534932 .bx-element-2534932-Ivo4o7U> *:first-child {padding: 18px 18px 18px 95px;text-align: left;background-image: url(\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png);background-position: 20% 50%;background-size: 12%;}.bxc.bx-campaign-2534932 .bx-element-2534932-PKwLHFJ {padding: 22px 0 0;}.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534932.bx-active-step-2 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-2 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534932 .bx-group-2534932-BO8GwHi {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932 .bx-group-2534932-BO8GwHi {display: none;}}.bxc.bx-campaign-2534932 .bx-element-2534932-kURMwTG {padding: 0;width: auto;}.bxc.bx-campaign-2534932 .bx-element-2534932-kURMwTG> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534932 .bx-group-2534932-b7aWr3P {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2534932 .bx-element-2534932-1S8R9dZ {padding: 16px 0 15px;}.bxc.bx-campaign-2534932 .bx-element-2534932-5b6qLna {padding: 16px 0 15px;}.bxc.bx-campaign-2534932 .bx-group-2534932-SVZLgNI {width: 280px;}.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534932.bx-active-step-3 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932.bx-active-step-3 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534932 .bx-group-2534932-56szcKo {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534932 .bx-group-2534932-56szcKo {display: none;}}.bxc.bx-campaign-2534932 .bx-element-2534932-z1GoOlV {padding: 0;width: auto;}.bxc.bx-campaign-2534932 .bx-element-2534932-z1GoOlV> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534932 .bx-group-2534932-g3NYL7l {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2534932 .bx-element-2534932-iLLRYfa {padding: 16px 0 15px;}.bxc.bx-campaign-2534932 .bx-element-2534932-qOr40Pv {padding: 16px 0 15px;}.bxc.bx-campaign-2534932 .bx-group-2534932-gy42ESn {width: 280px;}"
        },
        "2534938": {
            "ad_shown": false,
            "pid": "2534937",
            "pname": "Email + Text | Persistent | Part 4\/4 | Net-New | All Devices | Duplicate Email Text Capture",
            "purpose": "SMS Opt-In",
            "purpose_code": "smsoptin",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "smsoptin-entrance1",
            "name": "WKND | Persistent | Net-New | Overlay",
            "activations": [{
                "activation": "manual"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 1,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "reload",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 1,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg", "assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element: 'wunderkind spring 2024 sms and email sign up step3b',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id,\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind sms and email light box step3b impression',\n        link_name: 'wunderkind sms and email light box step3b impression_ 2534938_impression'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        link_name: 'wunderkind sms and email light box step3b_2534938 - clicks to get 25% off',\n        site_action : 'text signup',\n        action_location: 'wunderkind spring 2024 sms and email sign up'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind sms and email entrance lightbox 2024_step3b',\n        error_code : '', \/\/gets set in global campaign js\n        error_label : \"Phone Number\",\n        error_message : '', \/\/ DYNAMIC \"Invalid phone number\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);var newUser = 'AFJ0PlW',\n    duplicateUser = 'N1CDJuJ';\n\n\/* ---- EDIT ABOVE THIS LINE --- \"continue\" event *\/\nvar $dupeContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + duplicateUser + ' .bx-button'),\n    $newUserContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + newUser + ' .bx-button');\n\nbouncex.on($dupeContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType: 'continue',\n        customProps: {\n            event_name : 'wunderkind sms and email entrance lightbox 2024_step4b_2534938_you are already on the list_continue shopping',\n            custom_link_name : 'wunderkind sms and email entrance lightbox 2024_step4b_2534938_you are already on the list_continue shopping'\n        }\n    }); \/\/ default event uses \"already\" language\n    bouncex.off($dupeContinueButton, 'click.wknd' + ca_id);\n});\n\nbouncex.on($newUserContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType : 'continue',\n        customProps: {\n            event_name : 'wunderkind sms and email entrance lightbox 2024_step4a_2534938_you are on the list_continue shopping',\n            custom_link_name : 'wunderkind sms and email entrance lightbox 2024_step4a_2534938_you are on the list_continue shopping'\n        }\n    });\n    bouncex.off($newUserContinueButton, 'click.wknd' + ca_id);\n});\n\n\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e).indexOf('already been subscribed') > -1){\n        bouncex.setJumpStep(ca_id, 3);\n        bouncex.nextStep(ca_id);\n    }\n}); \n\n\/* Update SMS error message after 3 tries *\/\nvar errorCount = 0;\n\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n        errorCount++;\n        \n        if (errorCount > 2) {\n            jQuery('#bx-error-' + ca_id + '-phone_number').text('Please submit a different phone number');\n        }\n    }\n}); ",
                "click": "",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "SMS Opt-In - Entrance - Email + Text | Persistent | Part 4\/4 | Net-New | All Devices | Duplicate Email Text Capture (2534937): Overlay - variation - WKND | Persistent | Net-New | Overlay (2534938)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": "",
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 3,
            "closePlacement": ["inside", "inside", "inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2534938  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2534938\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2534938\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2534938\" class=\"bx-matte bx-shroud bx-shroud-2534938\"><\/div><div id=\"bx-hover-shroud-2534938\" class=\"bx-hover-shroud bx-hover-shroud-2534938\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2534938\" id=\"bx-creative-2534938\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2534938\" class=\"bx-ally-title\">Enter your phone number for 25% off your next order!<\/div><button id=\"bx-close-inside-2534938\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-JxZdKBq bx-step-2534938-1 bx-tail-placement-hidden\" id=\"bx-step-2534938-1\" data-close-placement=\"inside\"><form id=\"bx-form-2534938-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534938); return false\" onreset=\"bouncex.close_ad(2534938); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534938\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534938\" \/><div class=\"bx-group bx-group-primary bx-group-2534938-vXuzmT0 bx-group-vXuzmT0\" id=\"bx-group-2534938-vXuzmT0\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-7UNqIue bx-element-2534938-7UNqIue\" id=\"bx-element-2534938-7UNqIue\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-HXqq2Ji bx-group-HXqq2Ji\" id=\"bx-group-2534938-HXqq2Ji\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-PtiAXDs bx-element-2534938-PtiAXDs\" id=\"bx-element-2534938-PtiAXDs\"  ><div>You're already<br>on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-8LRkVoE bx-element-2534938-8LRkVoE\" id=\"bx-element-2534938-8LRkVoE\"  ><div>Get an extra 25% off*<br>when you sign up for texts too.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-gALco3y bx-group-gALco3y\" id=\"bx-group-2534938-gALco3y\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-cdnPOfK bx-element-2534938-cdnPOfK\" id=\"bx-element-2534938-cdnPOfK\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2534938-cdnPOfK-input\" class=\"bx-component bx-component-lPaDapg bx-component-2534938-lPaDapg bx-placeholder bx-component-placeholder\" id=\"bx-component-2534938-lPaDapg\"><span class=\"bx-placeholdertext\">Phone Number<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2534938-cdnPOfK-input\" type=\"tel\" name=\"phone_number\" placeholder=\"Phone Number\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-zOQ12eb bx-component-2534938-zOQ12eb bx-component-validation bx-vtext bx-error-2534938-phone_number\" id=\"bx-error-2534938-phone_number\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-sosumi  bx-row-pAYa3yQ bx-element-2534938-pAYa3yQ\" id=\"bx-element-2534938-pAYa3yQ\"  ><div>By entering your number, you consent to receive recurring autodialed marketing msgs to your mobile device. Consent not required to buy. Msg&data rates may apply. Customer Service: ************<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-p7eLrxm bx-element-2534938-p7eLrxm\" id=\"bx-element-2534938-p7eLrxm\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Get 25% Off <br> <span style=\"font-size:10px;\">By signing up for texts<\/span><\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-Rhu8dm6 bx-element-2534938-Rhu8dm6\" id=\"bx-element-2534938-Rhu8dm6\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-kuEr6kW bx-element-2534938-kuEr6kW\" id=\"bx-element-2534938-kuEr6kW\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >*Offer exclusions apply<\/a><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><div class=\"bx-step bx-step-2  bx-step-FeeCY1E bx-step-2534938-2 bx-tail-placement-hidden\" id=\"bx-step-2534938-2\" data-close-placement=\"inside\"><form id=\"bx-form-2534938-step-2\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534938); return false\" onreset=\"bouncex.close_ad(2534938); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534938\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534938\" \/><div class=\"bx-group bx-group-primary bx-group-2534938-BO8GwHi bx-group-BO8GwHi\" id=\"bx-group-2534938-BO8GwHi\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kURMwTG bx-element-2534938-kURMwTG\" id=\"bx-element-2534938-kURMwTG\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-b7aWr3P bx-group-b7aWr3P\" id=\"bx-group-2534938-b7aWr3P\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-1S8R9dZ bx-element-2534938-1S8R9dZ\" id=\"bx-element-2534938-1S8R9dZ\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-aXqgZ8K bx-element-2534938-aXqgZ8K\" id=\"bx-element-2534938-aXqgZ8K\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-5b6qLna bx-element-2534938-5b6qLna\" id=\"bx-element-2534938-5b6qLna\"  ><div>Check your texts for next steps.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-SVZLgNI bx-group-SVZLgNI\" id=\"bx-group-2534938-SVZLgNI\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-AFJ0PlW bx-element-2534938-AFJ0PlW\" id=\"bx-element-2534938-AFJ0PlW\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><div class=\"bx-step bx-step-3  bx-step-NLjKFpi bx-step-2534938-3 bx-tail-placement-hidden\" id=\"bx-step-2534938-3\" data-close-placement=\"inside\"><form id=\"bx-form-2534938-step-3\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2534938); return false\" onreset=\"bouncex.close_ad(2534938); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2534938\"><input type=\"hidden\" name=\"campaign_id\" value=\"2534938\" \/><div class=\"bx-group bx-group-primary bx-group-2534938-56szcKo bx-group-56szcKo\" id=\"bx-group-2534938-56szcKo\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-z1GoOlV bx-element-2534938-z1GoOlV\" id=\"bx-element-2534938-z1GoOlV\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-g3NYL7l bx-group-g3NYL7l\" id=\"bx-group-2534938-g3NYL7l\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-iLLRYfa bx-element-2534938-iLLRYfa\" id=\"bx-element-2534938-iLLRYfa\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-gH4iCNS bx-element-2534938-gH4iCNS\" id=\"bx-element-2534938-gH4iCNS\"  ><div>You're already on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-qOr40Pv bx-element-2534938-qOr40Pv\" id=\"bx-element-2534938-qOr40Pv\"  ><div>You'll still enjoy Macy's perks.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2534938-gy42ESn bx-group-gy42ESn\" id=\"bx-group-2534938-gy42ESn\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-N1CDJuJ bx-element-2534938-N1CDJuJ\" id=\"bx-element-2534938-N1CDJuJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2534938\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2534938 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2534938 *\/\/* custom css from creative 91474 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2534938 *\/.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534938.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534938 .bx-group-2534938-vXuzmT0 {padding: 0 0 25px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938 .bx-group-2534938-vXuzmT0 {display: none;}}.bxc.bx-campaign-2534938 .bx-element-2534938-7UNqIue {padding: 0;width: auto;}.bxc.bx-campaign-2534938 .bx-element-2534938-7UNqIue> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534938 .bx-group-2534938-HXqq2Ji {width: 320px;padding: 0 0 35px;}.bxc.bx-campaign-2534938 .bx-element-2534938-PtiAXDs {padding: 0 0 15px;}.bxc.bx-campaign-2534938 .bx-group-2534938-gALco3y {width: 280px;}.bxc.bx-campaign-2534938 .bx-element-2534938-cdnPOfK .bx-el {padding: 11.5px;}.bxc.bx-campaign-2534938 .bx-element-2534938-cdnPOfK .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2534938 .bx-has-text.bx-element-2534938-cdnPOfK .bx-el {padding: 11.5px;}.bxc.bx-campaign-2534938 .bx-row-validation .bx-component-2534938-zOQ12eb {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2534938 .bx-has-focus.bx-row-validation .bx-component-2534938-zOQ12eb {color: #ec0000;}.bxc.bx-campaign-2534938 .bx-element-2534938-pAYa3yQ {width: auto;padding: 0;}.bxc.bx-campaign-2534938 .bx-element-2534938-pAYa3yQ> *:first-child {padding: 10px 5px ;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;line-height: 1.2em;}.bxc.bx-campaign-2534938 .bx-element-2534938-p7eLrxm> *:first-child {padding: 18px 18px 18px 95px;text-align: left;background-image: url(\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png);background-position: 20% 50%;background-size: 12%;}.bxc.bx-campaign-2534938 .bx-element-2534938-Rhu8dm6 {padding: 22px 0 0;}.bxc.bx-campaign-2534938 .bx-element-2534938-kuEr6kW {padding: 35px 0 0;width: 100%;}.bxc.bx-campaign-2534938 .bx-element-2534938-kuEr6kW> *:first-child {font-style: italic;font-size: 12px;}.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534938.bx-active-step-2 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-2 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534938 .bx-group-2534938-BO8GwHi {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938 .bx-group-2534938-BO8GwHi {display: none;}}.bxc.bx-campaign-2534938 .bx-element-2534938-kURMwTG {padding: 0;width: auto;}.bxc.bx-campaign-2534938 .bx-element-2534938-kURMwTG> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534938 .bx-group-2534938-b7aWr3P {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2534938 .bx-element-2534938-1S8R9dZ {padding: 16px 0 15px;}.bxc.bx-campaign-2534938 .bx-element-2534938-5b6qLna {padding: 16px 0 15px;}.bxc.bx-campaign-2534938 .bx-group-2534938-SVZLgNI {width: 280px;}.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2534938.bx-active-step-3 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938.bx-active-step-3 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2534938 .bx-group-2534938-56szcKo {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2534938 .bx-group-2534938-56szcKo {display: none;}}.bxc.bx-campaign-2534938 .bx-element-2534938-z1GoOlV {padding: 0;width: auto;}.bxc.bx-campaign-2534938 .bx-element-2534938-z1GoOlV> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2534938 .bx-group-2534938-g3NYL7l {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2534938 .bx-element-2534938-iLLRYfa {padding: 16px 0 15px;}.bxc.bx-campaign-2534938 .bx-element-2534938-qOr40Pv {padding: 16px 0 15px;}.bxc.bx-campaign-2534938 .bx-group-2534938-gy42ESn {width: 280px;}"
        },
        "2842942": {
            "ad_shown": false,
            "pid": "2842941",
            "pname": "Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 1\/3",
            "purpose": "Email Capture",
            "purpose_code": "emailcapture",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "emailcapture-entrance2",
            "name": "WKND | PGV 1 | Email Step 1",
            "activations": [{
                "activation": "timer",
                "val": "0"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 0,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "",
            "activation_delay": 2,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 1,
            "mao": 4,
            "map": 1,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 0,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element:'wunderkind entrance lightbox 2025 sms and email sign up',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind entrance lightbox 2025 sms and email lightbox impression',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email sign up step1_2842942_impression'\n    },\n    'click' : {\n        event_name : 'content link click',\n        link_name : 'wunderkind entrance lightbox 2025 sms and email sign up step1_2842942_click to claim 25% off'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        site_action : 'email signup',\n        action_location: 'wunderkind entrance lightbox 2025 sms and email sign up',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email sign up step2_2842942_get 25% off'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind entrance lightbox 2025 sms and email lightbox_step2',\n        error_code : ca_id,\n        error_label : \"Email Address\",\n        error_message : '', \/\/ DYNAMIC \"Invalid Email Address\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nbouncex.tryCatch(function() {\n    var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.experiment', { experimentation_ids : [ ca_id ]});\n\n    bouncex.utagReport({\n        eventType : 'experiment',\n        customProps : customProps\n    });\n    \n    \/* 8\/2 for data validation *\/ \n    var expData = { \n        expIds : customProps.experimentation_ids[0],\n        campaignId: ca_id\n    };\n    \n    bouncex.push(['experiment', expData]);  \n})();var phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    bouncex.log('validateform', bouncex.utils.getNestedProp('response.errors.email', '', e));\n    if (bouncex.utils.getNestedProp('response.errors.email', '', e).indexOf('already been subscribed') > -1){\n        bouncex.close_ad(ca_id);\n    }\n}); ",
                "click": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.click', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'click',\n    customProps : customProps\n}, impressionData);",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();if (bouncex.cookie.campaigns[ca_id].ls) {\n    bouncex.show_ad(2846223);\n} else if (bouncex.cookie.es) {\n    bouncex.show_ad(2846225);\n}"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "Email Capture - Entrance - Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 1\/3 (2842941): Overlay - variation - WKND | PGV 1 | Email Step 1 (2842942)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": null,
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 1,
            "numSteps": 2,
            "closePlacement": ["inside", "inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2842942  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2842942\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2842942\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2842942\" class=\"bx-matte bx-shroud bx-shroud-2842942\"><\/div><div id=\"bx-hover-shroud-2842942\" class=\"bx-hover-shroud bx-hover-shroud-2842942\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2842942\" id=\"bx-creative-2842942\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2842942\" class=\"bx-ally-title\">Enter your email for 25% off your first order!<\/div><button id=\"bx-close-inside-2842942\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-JJ2yTEq bx-step-2842942-1 bx-tail-placement-hidden\" id=\"bx-step-2842942-1\" data-close-placement=\"inside\"><form id=\"bx-form-2842942-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2842942); return false\" onreset=\"bouncex.close_ad(2842942); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2842942\"><input type=\"hidden\" name=\"campaign_id\" value=\"2842942\" \/><div class=\"bx-group bx-group-primary bx-group-2842942-T7VrUGp bx-group-T7VrUGp\" id=\"bx-group-2842942-T7VrUGp\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-VUyOjFb bx-element-2842942-VUyOjFb\" id=\"bx-element-2842942-VUyOjFb\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2842942-5m9A5zw bx-group-5m9A5zw\" id=\"bx-group-2842942-5m9A5zw\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-GajDXOx bx-element-2842942-GajDXOx\" id=\"bx-element-2842942-GajDXOx\"  ><div>Hey friend!<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-Wrp5y1x bx-element-2842942-Wrp5y1x\" id=\"bx-element-2842942-Wrp5y1x\"  ><div>Interested in 25% off*<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-5kiXjea bx-element-2842942-5kiXjea\" id=\"bx-element-2842942-5kiXjea\"  ><div>your first order?<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2842942-BOoFlNf bx-group-BOoFlNf\" id=\"bx-group-2842942-BOoFlNf\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-Obijgh2 bx-element-2842942-Obijgh2\" id=\"bx-element-2842942-Obijgh2\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"0\" >Click to Claim 25% Off<\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-jwrcT8G bx-element-2842942-jwrcT8G\" id=\"bx-element-2842942-jwrcT8G\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-h3EdsHn bx-element-2842942-h3EdsHn\" id=\"bx-element-2842942-h3EdsHn\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >*Offer exclusions apply<\/a><\/a><\/div><\/div><\/form><\/div><div class=\"bx-step bx-step-2  bx-step-rFM6eW2 bx-step-2842942-2 bx-tail-placement-hidden\" id=\"bx-step-2842942-2\" data-close-placement=\"inside\"><form id=\"bx-form-2842942-step-2\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2842942); return false\" onreset=\"bouncex.close_ad(2842942); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2842942\"><input type=\"hidden\" name=\"campaign_id\" value=\"2842942\" \/><div class=\"bx-group bx-group-primary bx-group-2842942-sLB1iY3 bx-group-sLB1iY3\" id=\"bx-group-2842942-sLB1iY3\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-sEmLxRy bx-element-2842942-sEmLxRy\" id=\"bx-element-2842942-sEmLxRy\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2842942-wMtK9lt bx-group-wMtK9lt\" id=\"bx-group-2842942-wMtK9lt\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-7Q02G7Q bx-element-2842942-7Q02G7Q\" id=\"bx-element-2842942-7Q02G7Q\"  ><div>Unlock 25% off*<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-e0clKf2 bx-element-2842942-e0clKf2\" id=\"bx-element-2842942-e0clKf2\"  ><div>your first order when you join our list.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2842942-sqEkLIh bx-group-sqEkLIh\" id=\"bx-group-2842942-sqEkLIh\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-aiDVbjg bx-element-2842942-aiDVbjg\" id=\"bx-element-2842942-aiDVbjg\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2842942-aiDVbjg-input\" class=\"bx-component bx-component-hN4P652 bx-component-2842942-hN4P652 bx-placeholder bx-component-placeholder\" id=\"bx-component-2842942-hN4P652\"><span class=\"bx-placeholdertext\">Your Email Here<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2842942-aiDVbjg-input\" type=\"email\" name=\"email\" placeholder=\"Your Email Here\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-NbbQijc bx-component-2842942-NbbQijc bx-component-validation bx-vtext bx-error-2842942-email\" id=\"bx-error-2842942-email\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-GmylQGJ bx-element-2842942-GmylQGJ\" id=\"bx-element-2842942-GmylQGJ\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Get 25% Off<\/button><\/a><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-p83Rzy0 bx-element-2842942-p83Rzy0\" id=\"bx-element-2842942-p83Rzy0\"  ><div>By continuing, you agree to Macy's <a href=\"https:\/\/customerservice-macys.com\/articles\/macys-and-macyscom-notice-of-privacy-practices-2#collect.info\" style=\"text-decoration: underline;\" target=\"_blank\">Privacy Practices<\/a>. <\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-Fq1JsIn bx-element-2842942-Fq1JsIn\" id=\"bx-element-2842942-Fq1JsIn\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >*Offer exclusions apply<\/a><\/a><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2842942-VdXcRql bx-group-VdXcRql\" id=\"bx-group-2842942-VdXcRql\"  ><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-rUPMwaw bx-element-2842942-rUPMwaw\" id=\"bx-element-2842942-rUPMwaw\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2842942\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close email sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2842942 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2842942 *\/\/* custom css from creative 70947 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2842942 *\/.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2842942.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2842942 .bx-group-2842942-T7VrUGp {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-group-2842942-T7VrUGp {display: none;}}.bxc.bx-campaign-2842942 .bx-element-2842942-VUyOjFb {padding: 0;width: auto;}.bxc.bx-campaign-2842942 .bx-element-2842942-VUyOjFb> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2842942 .bx-group-2842942-5m9A5zw {width: 300px;padding: 0 0 35px;}.bxc.bx-campaign-2842942 .bx-element-2842942-Wrp5y1x {padding: 16px 0 15px;}.bxc.bx-campaign-2842942 .bx-group-2842942-BOoFlNf {width: 280px;}.bxc.bx-campaign-2842942 .bx-element-2842942-jwrcT8G {padding: 22px 0 0;}.bxc.bx-campaign-2842942 .bx-element-2842942-h3EdsHn {padding: 60px 0 0;width: 100%;}.bxc.bx-campaign-2842942 .bx-element-2842942-h3EdsHn> *:first-child {font-style: italic;font-size: 12px;}.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2842942.bx-active-step-2 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942.bx-active-step-2 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2842942 .bx-group-2842942-sLB1iY3 {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-group-2842942-sLB1iY3 {display: none;}}.bxc.bx-campaign-2842942 .bx-element-2842942-sEmLxRy {padding: 0;width: auto;}.bxc.bx-campaign-2842942 .bx-element-2842942-sEmLxRy> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2842942 .bx-group-2842942-wMtK9lt {width: 300px;padding: 0 0 35px;}.bxc.bx-campaign-2842942 .bx-element-2842942-7Q02G7Q {padding: 0 0 15px;}.bxc.bx-campaign-2842942 .bx-group-2842942-sqEkLIh {width: 90%;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-group-2842942-sqEkLIh {width: 450px;}}.bxc.bx-campaign-2842942 .bx-element-2842942-aiDVbjg .bx-el {padding: 11.5px;}.bxc.bx-campaign-2842942 .bx-element-2842942-aiDVbjg .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2842942 .bx-has-text.bx-element-2842942-aiDVbjg .bx-el {padding: 11.5px;}.bxc.bx-campaign-2842942 .bx-element-2842942-aiDVbjg {width: 290px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-element-2842942-aiDVbjg {width: 100%;}}.bxc.bx-campaign-2842942 .bx-row-validation .bx-component-2842942-NbbQijc {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2842942 .bx-has-focus.bx-row-validation .bx-component-2842942-NbbQijc {color: #ec0000;}.bxc.bx-campaign-2842942 .bx-element-2842942-GmylQGJ {width: 290px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-element-2842942-GmylQGJ {width: 100%;}}.bxc.bx-campaign-2842942 .bx-element-2842942-p83Rzy0 {width: 90%;padding: 10px 0 0;}.bxc.bx-campaign-2842942 .bx-element-2842942-p83Rzy0> *:first-child {font-style: italic;font-size: 12px;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942 .bx-element-2842942-p83Rzy0> *:first-child {font-size: 10px;}}.bxc.bx-campaign-2842942 .bx-element-2842942-Fq1JsIn {width: auto;padding: 4px 0 0;}.bxc.bx-campaign-2842942 .bx-element-2842942-Fq1JsIn> *:first-child {font-style: italic;font-size: 12px;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2842942 .bx-element-2842942-Fq1JsIn> *:first-child {font-size: 10px;}}.bxc.bx-campaign-2842942 .bx-group-2842942-VdXcRql {padding: 30px 0 0;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2842942 .bx-group-2842942-VdXcRql {padding: 15px 0 0;}}.bxc.bx-campaign-2842942 .bx-element-2842942-rUPMwaw {padding: 22px 0 0;}"
        },
        "2846223": {
            "ad_shown": false,
            "pid": "2846222",
            "pname": "Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 2\/3",
            "purpose": "SMS Opt-In",
            "purpose_code": "smsoptin",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "smsoptin-entrance1",
            "name": "WKND | Persistent | Net-New | Overlay",
            "activations": [{
                "activation": "manual"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 1,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "reload",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 1,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg", "assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element:'wunderkind entrance lightbox 2025 sms and email sign up',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind entrance lightbox 2025 sms and email lightbox step3a impression',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email lightbox step3a_2846223_impression'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        site_action : 'text signup',\n        action_location: 'wunderkind entrance lightbox 2025 sms and email sign up',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email lightbox step3a_2846223 - clicks to sign up for texts'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind entrance lightbox 2025 sms and email lightbox_step3a',\n        error_code : ca_id,\n        error_label : \"Phone number\",\n        error_message : '', \/\/ DYNAMIC \"Invalid phone number\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);var newUser = 'AFJ0PlW',\n    duplicateUser = 'N1CDJuJ';\n\n\/* ---- EDIT ABOVE THIS LINE --- \"continue\" event *\/\nvar $dupeContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + duplicateUser + ' .bx-button'),\n    $newUserContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + newUser + ' .bx-button');\n\nbouncex.on($dupeContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType: 'continue',\n        customProps: {\n            event_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4b_2846223_you are already on the list_continue shopping',\n            custom_link_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4b_2846223_you are already on the list_continue shopping'\n        }\n    }); \/\/ default event uses \"already\" language\n    bouncex.off($dupeContinueButton, 'click.wknd' + ca_id);\n});\n\nbouncex.on($newUserContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType : 'continue',\n        customProps: {\n            event_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4a_2846223_you are on the list_continue shopping',\n            custom_link_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4a_2846223_you are on the list_continue shopping'\n        }\n    });\n    bouncex.off($newUserContinueButton, 'click.wknd' + ca_id);\n});\n\n\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e).indexOf('already been subscribed') > -1){\n        bouncex.setJumpStep(ca_id, 3);\n        bouncex.nextStep(ca_id);\n    }\n}); \n\n\/* Update SMS error message after 3 tries *\/\nvar errorCount = 0;\n\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n        errorCount++;\n        \n        if (errorCount > 2) {\n            jQuery('#bx-error-' + ca_id + '-phone_number').text('Please submit a different phone number');\n        }\n    }\n}); ",
                "click": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.click', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'click',\n    customProps : customProps\n}, impressionData);",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "SMS Opt-In - Entrance - Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 2\/3 (2846222): Overlay - variation - WKND | Persistent | Net-New | Overlay (2846223)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": "",
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 3,
            "closePlacement": ["inside", "inside", "inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2846223  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2846223\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2846223\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2846223\" class=\"bx-matte bx-shroud bx-shroud-2846223\"><\/div><div id=\"bx-hover-shroud-2846223\" class=\"bx-hover-shroud bx-hover-shroud-2846223\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2846223\" id=\"bx-creative-2846223\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2846223\" class=\"bx-ally-title\">Enter your phone number and sign up for texts too!<\/div><button id=\"bx-close-inside-2846223\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-4Vp3zd8 bx-step-2846223-1 bx-tail-placement-hidden\" id=\"bx-step-2846223-1\" data-close-placement=\"inside\"><form id=\"bx-form-2846223-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846223); return false\" onreset=\"bouncex.close_ad(2846223); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846223\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846223\" \/><div class=\"bx-group bx-group-primary bx-group-2846223-qHwrmeS bx-group-qHwrmeS\" id=\"bx-group-2846223-qHwrmeS\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kyKuYL1 bx-element-2846223-kyKuYL1\" id=\"bx-element-2846223-kyKuYL1\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-fshJyx6 bx-group-fshJyx6\" id=\"bx-group-2846223-fshJyx6\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-EKDXzpR bx-element-2846223-EKDXzpR\" id=\"bx-element-2846223-EKDXzpR\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-uM5i8iz bx-element-2846223-uM5i8iz\" id=\"bx-element-2846223-uM5i8iz\"  ><div>Sign up for texts too.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-csrCyXo bx-group-csrCyXo\" id=\"bx-group-2846223-csrCyXo\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-WefZKz0 bx-element-2846223-WefZKz0\" id=\"bx-element-2846223-WefZKz0\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2846223-WefZKz0-input\" class=\"bx-component bx-component-STiQRuB bx-component-2846223-STiQRuB bx-placeholder bx-component-placeholder\" id=\"bx-component-2846223-STiQRuB\"><span class=\"bx-placeholdertext\">Phone Number<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2846223-WefZKz0-input\" type=\"tel\" name=\"phone_number\" placeholder=\"Phone Number\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-P0M97kU bx-component-2846223-P0M97kU bx-component-validation bx-vtext bx-error-2846223-phone_number\" id=\"bx-error-2846223-phone_number\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-sosumi  bx-row-u0DHaTO bx-element-2846223-u0DHaTO\" id=\"bx-element-2846223-u0DHaTO\"  ><div>By entering your number, you consent to receive recurring autodialed marketing msgs to your mobile device. Consent not required to buy. Msg&data rates may apply. Customer Service: ************<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-Ivo4o7U bx-element-2846223-Ivo4o7U\" id=\"bx-element-2846223-Ivo4o7U\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Sign Up for Texts <br> <span style=\"font-size:10px;\"><\/span><\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-PKwLHFJ bx-element-2846223-PKwLHFJ\" id=\"bx-element-2846223-PKwLHFJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><div class=\"bx-step bx-step-2  bx-step-FeeCY1E bx-step-2846223-2 bx-tail-placement-hidden\" id=\"bx-step-2846223-2\" data-close-placement=\"inside\"><form id=\"bx-form-2846223-step-2\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846223); return false\" onreset=\"bouncex.close_ad(2846223); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846223\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846223\" \/><div class=\"bx-group bx-group-primary bx-group-2846223-BO8GwHi bx-group-BO8GwHi\" id=\"bx-group-2846223-BO8GwHi\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kURMwTG bx-element-2846223-kURMwTG\" id=\"bx-element-2846223-kURMwTG\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-b7aWr3P bx-group-b7aWr3P\" id=\"bx-group-2846223-b7aWr3P\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-1S8R9dZ bx-element-2846223-1S8R9dZ\" id=\"bx-element-2846223-1S8R9dZ\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-aXqgZ8K bx-element-2846223-aXqgZ8K\" id=\"bx-element-2846223-aXqgZ8K\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-5b6qLna bx-element-2846223-5b6qLna\" id=\"bx-element-2846223-5b6qLna\"  ><div>Check your texts for next steps.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-SVZLgNI bx-group-SVZLgNI\" id=\"bx-group-2846223-SVZLgNI\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-AFJ0PlW bx-element-2846223-AFJ0PlW\" id=\"bx-element-2846223-AFJ0PlW\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><div class=\"bx-step bx-step-3  bx-step-NLjKFpi bx-step-2846223-3 bx-tail-placement-hidden\" id=\"bx-step-2846223-3\" data-close-placement=\"inside\"><form id=\"bx-form-2846223-step-3\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846223); return false\" onreset=\"bouncex.close_ad(2846223); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846223\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846223\" \/><div class=\"bx-group bx-group-primary bx-group-2846223-56szcKo bx-group-56szcKo\" id=\"bx-group-2846223-56szcKo\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-z1GoOlV bx-element-2846223-z1GoOlV\" id=\"bx-element-2846223-z1GoOlV\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-g3NYL7l bx-group-g3NYL7l\" id=\"bx-group-2846223-g3NYL7l\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-iLLRYfa bx-element-2846223-iLLRYfa\" id=\"bx-element-2846223-iLLRYfa\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-gH4iCNS bx-element-2846223-gH4iCNS\" id=\"bx-element-2846223-gH4iCNS\"  ><div>You're already on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-qOr40Pv bx-element-2846223-qOr40Pv\" id=\"bx-element-2846223-qOr40Pv\"  ><div>You'll still enjoy Macy's perks.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846223-gy42ESn bx-group-gy42ESn\" id=\"bx-group-2846223-gy42ESn\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-N1CDJuJ bx-element-2846223-N1CDJuJ\" id=\"bx-element-2846223-N1CDJuJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2846223\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2846223 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2846223 *\/\/* custom css from creative 91474 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2846223 *\/.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846223.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846223 .bx-group-2846223-qHwrmeS {padding: 0 0 25px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223 .bx-group-2846223-qHwrmeS {display: none;}}.bxc.bx-campaign-2846223 .bx-element-2846223-kyKuYL1 {padding: 0;width: auto;}.bxc.bx-campaign-2846223 .bx-element-2846223-kyKuYL1> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846223 .bx-group-2846223-fshJyx6 {width: 320px;padding: 0 0 35px;}.bxc.bx-campaign-2846223 .bx-element-2846223-EKDXzpR {padding: 0 0 15px;}.bxc.bx-campaign-2846223 .bx-group-2846223-csrCyXo {width: 280px;}.bxc.bx-campaign-2846223 .bx-element-2846223-WefZKz0 .bx-el {padding: 11.5px;}.bxc.bx-campaign-2846223 .bx-element-2846223-WefZKz0 .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2846223 .bx-has-text.bx-element-2846223-WefZKz0 .bx-el {padding: 11.5px;}.bxc.bx-campaign-2846223 .bx-row-validation .bx-component-2846223-P0M97kU {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2846223 .bx-has-focus.bx-row-validation .bx-component-2846223-P0M97kU {color: #ec0000;}.bxc.bx-campaign-2846223 .bx-element-2846223-u0DHaTO {width: auto;padding: 0;}.bxc.bx-campaign-2846223 .bx-element-2846223-u0DHaTO> *:first-child {padding: 10px 5px ;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;line-height: 1.2em;}.bxc.bx-campaign-2846223 .bx-element-2846223-Ivo4o7U> *:first-child {padding: 18px 18px 18px 95px;text-align: left;background-image: url(\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png);background-position: 20% 50%;background-size: 12%;}.bxc.bx-campaign-2846223 .bx-element-2846223-PKwLHFJ {padding: 22px 0 0;}.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846223.bx-active-step-2 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-2 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846223 .bx-group-2846223-BO8GwHi {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223 .bx-group-2846223-BO8GwHi {display: none;}}.bxc.bx-campaign-2846223 .bx-element-2846223-kURMwTG {padding: 0;width: auto;}.bxc.bx-campaign-2846223 .bx-element-2846223-kURMwTG> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846223 .bx-group-2846223-b7aWr3P {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2846223 .bx-element-2846223-1S8R9dZ {padding: 16px 0 15px;}.bxc.bx-campaign-2846223 .bx-element-2846223-5b6qLna {padding: 16px 0 15px;}.bxc.bx-campaign-2846223 .bx-group-2846223-SVZLgNI {width: 280px;}.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846223.bx-active-step-3 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223.bx-active-step-3 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846223 .bx-group-2846223-56szcKo {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846223 .bx-group-2846223-56szcKo {display: none;}}.bxc.bx-campaign-2846223 .bx-element-2846223-z1GoOlV {padding: 0;width: auto;}.bxc.bx-campaign-2846223 .bx-element-2846223-z1GoOlV> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846223 .bx-group-2846223-g3NYL7l {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2846223 .bx-element-2846223-iLLRYfa {padding: 16px 0 15px;}.bxc.bx-campaign-2846223 .bx-element-2846223-qOr40Pv {padding: 16px 0 15px;}.bxc.bx-campaign-2846223 .bx-group-2846223-gy42ESn {width: 280px;}"
        },
        "2846225": {
            "ad_shown": false,
            "pid": "2846224",
            "pname": "Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 3\/3 (Duplicate Email)",
            "purpose": "SMS Opt-In",
            "purpose_code": "smsoptin",
            "sub_purpose": "Entrance",
            "sub_purpose_code": "smsoptin-entrance1",
            "name": "WKND | Persistent | Net-New | Overlay",
            "activations": [{
                "activation": "manual"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 1,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "0",
            "blur_gate_exclusions": "0",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 1,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "reload",
            "activation_delay": 0,
            "closed_no_show": 0,
            "purchase_no_show": 0,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 0,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": true,
            "is_api": true,
            "osr_params_json": "",
            "is_man": 1,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 0,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "overlay",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "0",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "zoom-in",
            "te2": "zoom-in",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": "",
            "submission_redirect_delay": 2,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 0,
            "repress_bottom": 0,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg", "assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png"],
            "edw": 1,
            "event_js": {
                "preactivation": "bouncex.campaigns[ca_id].impression_data = {\n    link_element:'wunderkind entrance lightbox 2025 sms and email sign up',\n    zone_type: 'sms and mailsignup',\n    media_type: 'sms and email entrance lightbox',\n    attribute2: ca_id\n};\n\nbouncex.campaigns[ca_id].custom_utag_config = {\n    'impression' : {\n        event_name : 'content view',\n        impression_type : 'wunderkind entrance lightbox 2025 sms and email lightbox step3b impression',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email lightbox step3b_2846225_impression'\n    },\n    'submit' : {\n        event_name : 'content link click',\n        site_action : 'text signup',\n        action_location: 'wunderkind entrance lightbox 2025 sms and email sign up',\n        link_name: 'wunderkind entrance lightbox 2025 sms and email lightbox step3b_2846225 - clicks to sign up for texts'\n    },\n    'submit_error' : {\n        event_name : 'error link',\n        error_category : 'wunderkind entrance lightbox 2025 sms and email lightbox_step3b',\n        error_code : ca_id,\n        error_label : \"Phone number\",\n        error_message : '', \/\/ DYNAMIC \"Invalid phone number\"\n        error_scope : 'field', \/\/ [\"field\",\"global\"]\n        error_misc : ''\n    },\n};",
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "\/* VI-4996 *\/\nbouncex.addCss(' [aria-label=\"Live video shopping\"], [data-bambuser-liveshopping-floating-id] { z-index: 2147483644 !important; } ', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');\nvar $campaign = bouncex.campaigns[ca_id].obj1;\n\nbouncex.on($campaign.find('.bx-row-submit .bx-button[data-click=submit]'), 'click.wknd' + ca_id, function(){\n    var date = new Date(),\n    timeOptions = {\n        timeZone: \"PST\",\n        hour12: false ,\n        hour: 'numeric',\n        minute: 'numeric',\n        second: 'numeric',\n        fractionalSecondDigits : 3\n    },\n    localeDate = date.toLocaleDateString('en-US', {timeZone: \"PST\"}),\n    localeTime = date.toLocaleTimeString('en-US', timeOptions),\n    dateString = localeDate + ' ' + localeTime;\n    \n    $campaign.find('[name=date_formatted]').val(dateString);\n});\nvar phoneErrorCount = 0;\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e) {\n    if (e.campaign_id === ca_id && e.response && e.response.errors && (e.response.errors.email || e.response.errors.phone_number)) {\n        var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit_error', {});\n        \n        customProps.error_code = String(ca_id);\n        customProps.error_message = e.response.errors.email || e.response.errors.phone_number;\n        \n        if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n            phoneErrorCount++;\n            \n            if (phoneErrorCount > 2 && e.response.errors.phone_number.indexOf(\"This phone number has already been subscribed.\") === -1){\n                customProps.error_message = 'Please submit a different phone number';\n            }\n        }\n\n        bouncex.utagReport({\n            eventType : 'submit error',\n            customProps : customProps\n        });\n    }\n});var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);var newUser = 'AFJ0PlW',\n    duplicateUser = 'N1CDJuJ';\n\n\/* ---- EDIT ABOVE THIS LINE --- \"continue\" event *\/\nvar $dupeContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + duplicateUser + ' .bx-button'),\n    $newUserContinueButton = bouncex.campaigns[ca_id].obj1.find('#bx-element-' + ca_id + '-' + newUser + ' .bx-button');\n\nbouncex.on($dupeContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType: 'continue',\n        customProps: {\n            event_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4b_2846225_you are already on the list_continue shopping',\n            custom_link_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4b_2846225_you are already on the list_continue shopping'\n        }\n    }); \/\/ default event uses \"already\" language\n    bouncex.off($dupeContinueButton, 'click.wknd' + ca_id);\n});\n\nbouncex.on($newUserContinueButton, 'click.wknd' + ca_id, function(){\n    bouncex.utagReport({\n        eventType : 'continue',\n        customProps: {\n            event_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4a_2846225_you are on the list_continue shopping',\n            custom_link_name : 'wunderkind entrance lightbox 2025 sms and email lightbox_step4a_2846225_you are on the list_continue shopping'\n        }\n    });\n    bouncex.off($newUserContinueButton, 'click.wknd' + ca_id);\n});\n\n\/* https:\/\/www.wrike.com\/open.htm?id=1063634012 *\/\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e).indexOf('already been subscribed') > -1){\n        bouncex.setJumpStep(ca_id, 3);\n        bouncex.nextStep(ca_id);\n    }\n}); \n\n\/* Update SMS error message after 3 tries *\/\nvar errorCount = 0;\n\nbouncex.on(bouncex.document, 'bxValidateFormComplete.bx-'+ca_id, function(e){\n    if (bouncex.utils.getNestedProp('response.errors.phone_number', '', e)){\n        errorCount++;\n        \n        if (errorCount > 2) {\n            jQuery('#bx-error-' + ca_id + '-phone_number').text('Please submit a different phone number');\n        }\n    }\n}); ",
                "click": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.click', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'click',\n    customProps : customProps\n}, impressionData);",
                "submission": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.submit', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'submit',\n    customProps : customProps\n}, impressionData);",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "SMS Opt-In - Entrance - Email + Text | PGV 1 | Entrance | Net-New | All Devices | Step 3\/3 (Duplicate Email) (2846224): Overlay - variation - WKND | Persistent | Net-New | Overlay (2846225)"
            },
            "is_bpush": false,
            "z_index": "0",
            "push_optin_json": "",
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 3,
            "closePlacement": ["inside", "inside", "inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-2846225  bx-brand-31516 bx-width-default bx-type-overlay  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-2846225\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" aria-labelledby=\"bx-campaign-ally-title-2846225\" role=\"dialog\" aria-modal=\"true\"><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><div id=\"bx-shroud-2846225\" class=\"bx-matte bx-shroud bx-shroud-2846225\"><\/div><div id=\"bx-hover-shroud-2846225\" class=\"bx-hover-shroud bx-hover-shroud-2846225\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-2846225\" id=\"bx-creative-2846225\"><div class=\"bx-wrap\"><div id=\"bx-campaign-ally-title-2846225\" class=\"bx-ally-title\">Enter your phone number for 25% off your next order!<\/div><button id=\"bx-close-inside-2846225\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-JxZdKBq bx-step-2846225-1 bx-tail-placement-hidden\" id=\"bx-step-2846225-1\" data-close-placement=\"inside\"><form id=\"bx-form-2846225-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846225); return false\" onreset=\"bouncex.close_ad(2846225); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846225\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846225\" \/><div class=\"bx-group bx-group-primary bx-group-2846225-vXuzmT0 bx-group-vXuzmT0\" id=\"bx-group-2846225-vXuzmT0\"  ><input type=\"hidden\" name=\"date_formatted\" value=\"\" class=\"bx-el bx-input bx-input-hidden\"\/><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-7UNqIue bx-element-2846225-7UNqIue\" id=\"bx-element-2846225-7UNqIue\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-HXqq2Ji bx-group-HXqq2Ji\" id=\"bx-group-2846225-HXqq2Ji\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-PtiAXDs bx-element-2846225-PtiAXDs\" id=\"bx-element-2846225-PtiAXDs\"  ><div>You're already<br>on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-default  bx-row-8LRkVoE bx-element-2846225-8LRkVoE\" id=\"bx-element-2846225-8LRkVoE\"  ><div>Get an extra 25% off*<br>when you sign up for texts too.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-gALco3y bx-group-gALco3y\" id=\"bx-group-2846225-gALco3y\"  ><div class=\"bx-row bx-row-input bx-row-input-label  bx-row-placeholder-top bx-row-cdnPOfK bx-element-2846225-cdnPOfK\" id=\"bx-element-2846225-cdnPOfK\"  ><div class=\"bx-inputwrap\"><label for=\"bx-element-2846225-cdnPOfK-input\" class=\"bx-component bx-component-lPaDapg bx-component-2846225-lPaDapg bx-placeholder bx-component-placeholder\" id=\"bx-component-2846225-lPaDapg\"><span class=\"bx-placeholdertext\">Phone Number<\/span><\/label><input class=\"bx-el bx-input\" id=\"bx-element-2846225-cdnPOfK-input\" type=\"tel\" name=\"phone_number\" placeholder=\"Phone Number\" aria-required=\"true\"\/><\/div><div class=\"bx-component bx-component-zOQ12eb bx-component-2846225-zOQ12eb bx-component-validation bx-vtext bx-error-2846225-phone_number\" id=\"bx-error-2846225-phone_number\">Please enter above<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-sosumi  bx-row-pAYa3yQ bx-element-2846225-pAYa3yQ\" id=\"bx-element-2846225-pAYa3yQ\"  ><div>By entering your number, you consent to receive recurring autodialed marketing msgs to your mobile device. Consent not required to buy. Msg&data rates may apply. Customer Service: ************<\/div><\/div><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-p7eLrxm bx-element-2846225-p7eLrxm\" id=\"bx-element-2846225-p7eLrxm\"  ><button type=\"submit\" class=\"bx-button\" data-click=\"submit\" data-step-delay=\"0\" data-submit-jump=\"0\" data-submit-force=\"1\" >Get 25% Off <br> <span style=\"font-size:10px;\">By signing up for texts<\/span><\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-Rhu8dm6 bx-element-2846225-Rhu8dm6\" id=\"bx-element-2846225-Rhu8dm6\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" aria-label=\"Decline Offer; close the dialog\">Decline Offer<\/button><\/a><\/div><div class=\"bx-row bx-row-submit bx-row-submit-no  bx-row-kuEr6kW bx-element-2846225-kuEr6kW\" id=\"bx-element-2846225-kuEr6kW\"  ><a href=\"https:\/\/www.macys.com\/s\/promotional-details\/25-signup-offer\/\" target=\"_blank\" class=\"bx-button\" data-click=\"hyperlink\" data-click-report=\"nothing\" >*Offer exclusions apply<\/a><\/a><\/div><\/div><input autocomplete=\"carb-trap\" type=\"input\" name=\"carb-trap\" tabindex=\"-1\" aria-hidden=\"true\" class=\"bx-input bx-carb-trap\"\/><\/form><\/div><div class=\"bx-step bx-step-2  bx-step-FeeCY1E bx-step-2846225-2 bx-tail-placement-hidden\" id=\"bx-step-2846225-2\" data-close-placement=\"inside\"><form id=\"bx-form-2846225-step-2\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846225); return false\" onreset=\"bouncex.close_ad(2846225); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846225\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846225\" \/><div class=\"bx-group bx-group-primary bx-group-2846225-BO8GwHi bx-group-BO8GwHi\" id=\"bx-group-2846225-BO8GwHi\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-kURMwTG bx-element-2846225-kURMwTG\" id=\"bx-element-2846225-kURMwTG\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-b7aWr3P bx-group-b7aWr3P\" id=\"bx-group-2846225-b7aWr3P\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-1S8R9dZ bx-element-2846225-1S8R9dZ\" id=\"bx-element-2846225-1S8R9dZ\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-aXqgZ8K bx-element-2846225-aXqgZ8K\" id=\"bx-element-2846225-aXqgZ8K\"  ><div>You're on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-5b6qLna bx-element-2846225-5b6qLna\" id=\"bx-element-2846225-5b6qLna\"  ><div>Check your texts for next steps.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-SVZLgNI bx-group-SVZLgNI\" id=\"bx-group-2846225-SVZLgNI\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-AFJ0PlW bx-element-2846225-AFJ0PlW\" id=\"bx-element-2846225-AFJ0PlW\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><div class=\"bx-step bx-step-3  bx-step-NLjKFpi bx-step-2846225-3 bx-tail-placement-hidden\" id=\"bx-step-2846225-3\" data-close-placement=\"inside\"><form id=\"bx-form-2846225-step-3\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(2846225); return false\" onreset=\"bouncex.close_ad(2846225); return false\" tabindex=\"0\" aria-labelledby=\"bx-campaign-ally-title-2846225\"><input type=\"hidden\" name=\"campaign_id\" value=\"2846225\" \/><div class=\"bx-group bx-group-primary bx-group-2846225-56szcKo bx-group-56szcKo\" id=\"bx-group-2846225-56szcKo\"  ><div class=\"bx-row bx-row-image bx-row-image-logo  bx-row-z1GoOlV bx-element-2846225-z1GoOlV\" id=\"bx-element-2846225-z1GoOlV\"  ><img src=\"\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/71bce41f8ef8c8c867aeaf11b665d3bb.svg\" alt=\"Macy's\"\/><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-g3NYL7l bx-group-g3NYL7l\" id=\"bx-group-2846225-g3NYL7l\"  ><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-iLLRYfa bx-element-2846225-iLLRYfa\" id=\"bx-element-2846225-iLLRYfa\"  ><div>Woo hoo! <\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-gH4iCNS bx-element-2846225-gH4iCNS\" id=\"bx-element-2846225-gH4iCNS\"  ><div>You're already on the list.<\/div><\/div><div class=\"bx-row bx-row-text bx-row-text-subheadline  bx-row-qOr40Pv bx-element-2846225-qOr40Pv\" id=\"bx-element-2846225-qOr40Pv\"  ><div>You'll still enjoy Macy's perks.<\/div><\/div><\/div><div class=\"bx-group bx-group-default bx-group-2846225-gy42ESn bx-group-gy42ESn\" id=\"bx-group-2846225-gy42ESn\"  ><div class=\"bx-row bx-row-submit bx-row-submit-default  bx-row-N1CDJuJ bx-element-2846225-N1CDJuJ\" id=\"bx-element-2846225-N1CDJuJ\"  ><button type=\"reset\" class=\"bx-button\" data-click=\"close\" data-click-report=\"nothing\" aria-label=\"Continue Shopping; close the dialog\">Continue Shopping<\/button><\/a><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-2846225\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close sign up dialog<\/span><\/button><button class=\"bx-ally-tab-decoy\" aria-hidden=\"true\"><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-2846225 *\/\/* zoom-in *\/@-webkit-keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }@keyframes bx-fx-zoom-in {  0% {    -webkit-transform: scale(0.7);    transform: scale(0.7);    opacity: 0; }  100% {    -webkit-transform: scale(1);    transform: scale(1);    opacity: 1; } }.bxc.bx-base.bx-fx-zoom-in.bx-impress-in .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9); }.bxc.bx-base.bx-fx-zoom-in.bx-impress-out .bx-creative {  -webkit-animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse;  animation: bx-fx-zoom-in 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9) reverse; }\/* custom css .bx-campaign-2846225 *\/\/* custom css from creative 91474 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-2846225 *\/.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846225.bx-active-step-1 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-1 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846225 .bx-group-2846225-vXuzmT0 {padding: 0 0 25px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225 .bx-group-2846225-vXuzmT0 {display: none;}}.bxc.bx-campaign-2846225 .bx-element-2846225-7UNqIue {padding: 0;width: auto;}.bxc.bx-campaign-2846225 .bx-element-2846225-7UNqIue> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846225 .bx-group-2846225-HXqq2Ji {width: 320px;padding: 0 0 35px;}.bxc.bx-campaign-2846225 .bx-element-2846225-PtiAXDs {padding: 0 0 15px;}.bxc.bx-campaign-2846225 .bx-group-2846225-gALco3y {width: 280px;}.bxc.bx-campaign-2846225 .bx-element-2846225-cdnPOfK .bx-el {padding: 11.5px;}.bxc.bx-campaign-2846225 .bx-element-2846225-cdnPOfK .bx-el:focus {background-color: transparent;border-color: #000000;color: #000000;}.bxc.bx-campaign-2846225 .bx-has-text.bx-element-2846225-cdnPOfK .bx-el {padding: 11.5px;}.bxc.bx-campaign-2846225 .bx-row-validation .bx-component-2846225-zOQ12eb {padding: 5px 0 0;position: static;font-size: 12px;color: #ec0000;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-campaign-2846225 .bx-has-focus.bx-row-validation .bx-component-2846225-zOQ12eb {color: #ec0000;}.bxc.bx-campaign-2846225 .bx-element-2846225-pAYa3yQ {width: auto;padding: 0;}.bxc.bx-campaign-2846225 .bx-element-2846225-pAYa3yQ> *:first-child {padding: 10px 5px ;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;line-height: 1.2em;}.bxc.bx-campaign-2846225 .bx-element-2846225-p7eLrxm> *:first-child {padding: 18px 18px 18px 95px;text-align: left;background-image: url(\/\/assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/e6f83dce1a1f690c28e1a83fd9189fb4.png);background-position: 20% 50%;background-size: 12%;}.bxc.bx-campaign-2846225 .bx-element-2846225-Rhu8dm6 {padding: 22px 0 0;}.bxc.bx-campaign-2846225 .bx-element-2846225-kuEr6kW {padding: 35px 0 0;width: 100%;}.bxc.bx-campaign-2846225 .bx-element-2846225-kuEr6kW> *:first-child {font-style: italic;font-size: 12px;}.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846225.bx-active-step-2 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-2 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846225 .bx-group-2846225-BO8GwHi {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225 .bx-group-2846225-BO8GwHi {display: none;}}.bxc.bx-campaign-2846225 .bx-element-2846225-kURMwTG {padding: 0;width: auto;}.bxc.bx-campaign-2846225 .bx-element-2846225-kURMwTG> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846225 .bx-group-2846225-b7aWr3P {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2846225 .bx-element-2846225-1S8R9dZ {padding: 16px 0 15px;}.bxc.bx-campaign-2846225 .bx-element-2846225-5b6qLna {padding: 16px 0 15px;}.bxc.bx-campaign-2846225 .bx-group-2846225-SVZLgNI {width: 280px;}.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative> *:first-child {width: 400px;padding: 0;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative> *:first-child {padding: 0;width: 330px;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative> *:first-child {width: 600px;padding: 20px;}}@media all and (min-width: 415px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative> *:first-child {width: 416px;}}.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative:before {min-height: 600px;}.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative {width: auto;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative {background-image: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-creative:before {min-height: 370px;}}.bxc.bx-campaign-2846225.bx-active-step-3 .bx-shroud {background-size: cover;}@media all and (max-width: 414px) and (orientation: portrait) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-powered> *:first-child {display: none;}}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225.bx-active-step-3 .bx-powered> *:first-child {display: none;}}.bxc.bx-campaign-2846225 .bx-group-2846225-56szcKo {padding: 0 0 42px;}@media all and (max-width: 823px) and (orientation: landscape) {.bxc.bx-campaign-2846225 .bx-group-2846225-56szcKo {display: none;}}.bxc.bx-campaign-2846225 .bx-element-2846225-z1GoOlV {padding: 0;width: auto;}.bxc.bx-campaign-2846225 .bx-element-2846225-z1GoOlV> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-campaign-2846225 .bx-group-2846225-g3NYL7l {width: 300px;padding: 0 0 20px;}.bxc.bx-campaign-2846225 .bx-element-2846225-iLLRYfa {padding: 16px 0 15px;}.bxc.bx-campaign-2846225 .bx-element-2846225-qOr40Pv {padding: 16px 0 15px;}.bxc.bx-campaign-2846225 .bx-group-2846225-gy42ESn {width: 280px;}"
        }
    },
    "gbi2_enabled": false,
    "ssp_config": false,
    "push_filepath": "\/",
    "sms_enabled": true,
    "sms_terms_conditions_link": "",
    "sms_short_codes": [],
    "brand_name": "Macy's",
    "ads_config": {},
    "visit_cookie_updates": {
        "is_go_session": false,
        "is_gp_session": false
    }
});
