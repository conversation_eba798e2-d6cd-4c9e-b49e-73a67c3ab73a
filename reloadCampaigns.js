//<script>
bouncex.tryCatch(function reloadCampaigns() {
    var newCampaigns = {
        "1578374": {
            "ad_shown": false,
            "pid": "1578358",
            "pname": "Continuity | All Devices | Check your Inbox",
            "purpose": "Continuity",
            "purpose_code": "continuity",
            "sub_purpose": "Continuity",
            "sub_purpose_code": "continuity2",
            "name": "Continuity | All Devices",
            "activations": [{
                "activation": "timer",
                "prop": "",
                "val": "0"
            }],
            "overlay": "none",
            "compliance": {
                "gdpr": 0,
                "gmp": 0,
                "casl": 0
            },
            "coverlay": "none",
            "ctop": "none",
            "cbottom": "none",
            "bottom": "none",
            "callout": "none",
            "callout_t": "",
            "callout_pt": null,
            "callout_pos": "lb",
            "header_bottom_alignment": "center",
            "callout_anchor_pos": "lm",
            "blur_gate_enabled": 0,
            "blur_gate_inclusions": "",
            "blur_gate_exclusions": "",
            "dom_placement_method": "",
            "callout_voffset": "0",
            "callout_hoffset": "0",
            "acas": 0,
            "top": "none",
            "overlay_teleport_html": "",
            "overlay_teleport_type": "_blank",
            "opacity": 0.90000000000000002,
            "color": "#000000",
            "close_button_delay": 0,
            "custom_tab_title": {
                "title": null,
                "favicon_url": null,
                "favicon_type": "",
                "effect": null
            },
            "show_close": 0,
            "show_close_step": 0,
            "close_redirect_url": "",
            "close_redirect_type": "",
            "activation_delay": 0,
            "closed_no_show": 1,
            "purchase_no_show": 1,
            "ipc": 0,
            "mas": 0,
            "mao": 0,
            "map": 1,
            "iao": "0",
            "rao": "0",
            "tvao": "0",
            "ignore_activation_offset_page": "no_landing",
            "right_activation_offset_page": "no_landing",
            "top_vertical_activation_offset_page": "no_landing",
            "is_ec": false,
            "is_api": false,
            "osr_params_json": "",
            "is_man": 0,
            "ad_auto_close": 0,
            "activation_offset": 0,
            "header_top_nano": 1,
            "header_bottom_nano": 0,
            "shroud_on_hover": 0,
            "type": "nanobar",
            "ttype": "variation",
            "hbna": "0",
            "hbnbg": "0",
            "htna": "landing",
            "htnbg": "1",
            "ad_visible": false,
            "osfn": "",
            "tes": "1",
            "te": "no_effect",
            "te2": "no_effect",
            "t_valign": "top",
            "b_valign": "bottom",
            "qbxtest": false,
            "submission_redirect": null,
            "submission_redirect_delay": 0,
            "osfn_website": " ",
            "supress_overlay": 0,
            "repress_overlay": 0,
            "supress_top": 0,
            "repress_top": 0,
            "supress_bottom": 1,
            "repress_bottom": 1,
            "ng": 1,
            "images": ["assets.bounceexchange.com\/assets\/uploads\/clients\/4994\/creatives\/6bed162f644ec7a5c0bfceb51822d955.svg"],
            "edw": 0,
            "event_js": {
                "activation": "bouncex.campaigns[ca_id].obj1.find('form:not(:has(.bx-2-heading))').each(function(i, formEl) {\n    var $htmlHeadingElement = jQuery(formEl).find('h1, h2, h3, h4, h5, h6').eq(0);\n    if ($htmlHeadingElement.length > 0) {\n        $htmlHeadingElement.addClass('bx-2-heading wknd-ally-focus').attr('tabindex', '-1');\n    } else {\n        var $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text-headline, .bx-row-text-subheadline)').eq(0);\n        if ($headlineGroup.length === 0) {\n           $headlineGroup = jQuery(formEl).find('.bx-group:has(.bx-row-text)').eq(0);\n        }\n        $headlineGroup.find('.bx-row-text:first').nextUntil(':not(.bx-row-text, .bx-row-coupon)').addBack().wrapAll('<div class=\"bx-2-heading wknd-ally-focus\" id=\"'+$headlineGroup.attr('id')+'-h2\" tabindex=\"-1\" role=\"heading\" aria-level=\"2\"><\/div>'); \n    }\n});bouncex.campaigns[ca_id].obj1.find('form').addClass('bx-ally-no-focus').removeAttr('tabindex role');",
                "impression": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.impression', {}),\n    eventConfig = {\n        eventType : 'impression',\n        customProps : customProps\n    },\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport(eventConfig, impressionData);bouncex.addCss('#bx-campaign-' + campaign_id + ' .bx-slab, #bx-campaign-' + campaign_id + ' .bx-close { z-index: 6; }', bouncex.campaigns[campaign_id].obj1.get(0), 'bx-campaign-'+campaign_id+'-indexFix');var $campaign = bouncex.campaigns[campaign_id].obj1,\n    $creative = $campaign.find('.bx-creative');\n\nif ($campaign.find('#bx-campaign-'+campaign_id+'-pushdown-style').length === 0) {\n    bouncex.addCss('', $campaign.get(0), 'bx-campaign-'+campaign_id+'-pushdown');\n}\n\nfunction updatePushdown() {\n    var pushAmount = $creative.height();\n    \n    var styles = [\n        '#bx-campaign-'+campaign_id+' .bx-slab { position: static; }',\n        \/\/ '.overlay:not(.mobile-navigation) { margin-top: '+pushAmount+'px !important; }',\n        '.overlay.navigation[ref_key=\"$overlay\"] { top: calc(180px + '+pushAmount+'px); }',\n        '.overlay.link-rail[ref_key=\"$overlay\"] { top: calc(43px + '+pushAmount+'px); }'\n    ];\n    \n    $campaign.find('#bx-campaign-'+campaign_id+'-pushdown-style').text(styles.join(' '));\n}\n\nbouncex.on(bouncex.window, 'resize.bx-'+campaign_id, updatePushdown);\n\nupdatePushdown();",
                "click": "var customProps = bouncex.utils.getNestedProp('bouncex.campaigns[' + ca_id + '].custom_utag_config.click', {}),\n    impressionData = bouncex.campaigns[ca_id].impression_data || { attribute2 : ca_id };\n\nbouncex.utagReport({\n    eventType : 'click',\n    customProps : customProps\n}, impressionData);",
                "submission": "",
                "close": "jQuery('#bx-campaign-'+campaign_id+'-indexFix-style').remove();jQuery('#bx-campaign-'+campaign_id+'-pushdown-style').remove();\nbouncex.off(bouncex.window, 'resize.bx-'+campaign_id);"
            },
            "ga_i": {
                "impression": 1,
                "click": 1,
                "submission": 1,
                "label": "Continuity - Continuity - Continuity | All Devices | Check your Inbox (1578358): Top Bar - variation - Continuity | All Devices (1578374)"
            },
            "is_bpush": false,
            "z_index": null,
            "push_optin_json": null,
            "trigger_offsite_json": null,
            "suppress_element": null,
            "dynamic_anchor": 0,
            "new_session": true,
            "noCreative": false,
            "gbi": false,
            "noPostSubmit": 0,
            "numSteps": 1,
            "closePlacement": ["inside"],
            "control": false,
            "html": "<div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally   bx-campaign-1578374  bx-brand-31517 bx-width-full bx-type-nanobar  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-1578374\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" ><div id=\"bx-shroud-1578374\" class=\"bx-matte bx-shroud bx-shroud-1578374\"><\/div><div id=\"bx-hover-shroud-1578374\" class=\"bx-hover-shroud bx-hover-shroud-1578374\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-1578374\" id=\"bx-creative-1578374\"><div class=\"bx-wrap\"><button id=\"bx-close-inside-1578374\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close promotional banner<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-AS36VCz bx-step-1578374-1 bx-tail-placement-hidden\" id=\"bx-step-1578374-1\" data-close-placement=\"inside\"><form id=\"bx-form-1578374-step-1\" bx-novalidate=\"true\" method=\"post\" action=\"https:\/\/api.bounceexchange.com\/capture\/submit\" onsubmit=\"return bouncex.submitCampaignStep(1578374); return false\" onreset=\"bouncex.close_ad(1578374); return false\" tabindex=\"0\" ><input type=\"hidden\" name=\"campaign_id\" value=\"1578374\" \/><div class=\"bx-group bx-group-default bx-group-1578374-UL1Kmfv bx-group-UL1Kmfv\" id=\"bx-group-1578374-UL1Kmfv\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-HEFBD6M bx-element-1578374-HEFBD6M\" id=\"bx-element-1578374-HEFBD6M\"  ><div>So close! Check your inbox for your code.<\/div><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-1578374\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close promotional banner<\/span><\/button><\/div><div class=\"bxc bx-base bx-custom bx-active-step-1 bx-ally  bx-pusher bx-campaign-1578374  bx-brand-31517 bx-width-full bx-type-nanobar  bx-has-close-x-1 bx-has-close-inside\" id=\"bx-campaign-1578374-clone\" style=\"display:none; visibility:hidden;\" aria-hidden=\"true\" ><div id=\"bx-shroud-1578374-clone\" class=\"bx-matte bx-shroud bx-shroud-1578374\"><\/div><div id=\"bx-hover-shroud-1578374-clone\" class=\"bx-hover-shroud bx-hover-shroud-1578374\" style=\"display:none\"><\/div><div class=\"bx-slab\"><div class=\"bx-align\"><div class=\"bx-creative bx-creative-1578374\" id=\"bx-creative-1578374-clone\"><div class=\"bx-wrap\"><button id=\"bx-close-inside-1578374-clone\" class=\"bx-close bx-close-link bx-close-inside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close promotional banner<\/span><\/button><div class=\"bx-step bx-step-1 bx-active-step bx-step-AS36VCz bx-step-1578374-1 bx-tail-placement-hidden\" id=\"bx-step-1578374-1-clone\" data-close-placement=\"inside\"><form id=\"bx-form-1578374-step-1-clone\" bx-novalidate=\"true\" method=\"post\" action=\"\" onsubmit=\"\" onreset=\"bouncex.close_ad(1578374); return false\" tabindex=\"0\" ><input type=\"hidden\" name=\"campaign_id\" value=\"1578374\" \/><div class=\"bx-group bx-group-default bx-group-1578374-UL1Kmfv bx-group-UL1Kmfv\" id=\"bx-group-1578374-UL1Kmfv-clone\"  ><div class=\"bx-row bx-row-text bx-row-text-headline  bx-row-HEFBD6M bx-element-1578374-HEFBD6M\" id=\"bx-element-1578374-HEFBD6M-clone\"  ><div>So close! Check your inbox for your code.<\/div><\/div><\/div><\/form><\/div><\/div><\/div><\/div><\/div><button id=\"bx-close-outside-1578374-clone\" class=\"bx-close bx-close-link bx-close-outside\" data-click=\"close\"><svg class=\"bx-close-xsvg\" viewBox=\"240 240 20 20\" aria-hidden=\"true\"><g class=\"bx-close-xstroke bx-close-x-adaptive\"><path class=\"bx-close-x-adaptive-1\" d=\"M255.6 255.6l-11.2-11.2\" vector-effect=\"non-scaling-stroke\"\/><path class=\"bx-close-x-adaptive-2\" d=\"M255.6 244.4l-11.2 11.2\" vector-effect=\"non-scaling-stroke\"\/><\/g><\/svg><span class=\"bx-ally-label\">close promotional banner<\/span><\/button><\/div>",
            "styles": "\/* effects for .bx-campaign-1578374 *\/\/* custom css .bx-campaign-1578374 *\/\/* custom css from creative 70955 *\/\/************************************ CREATIVE STRUCTURE Do not remove or edit unless non applicable to creative set.************************************\/\/* rendered styles .bx-campaign-1578374 *\/.bxc.bx-campaign-1578374.bx-active-step-1 .bx-creative> *:first-child {width: 100%;padding: 12px 0;}@media all and (max-width: 736px) {.bxc.bx-campaign-1578374.bx-active-step-1 .bx-creative> *:first-child {width: 320px;}}@media all and (min-width: 737px) and (max-width: 1024px) {.bxc.bx-campaign-1578374.bx-active-step-1 .bx-creative> *:first-child {width: 737px;}}.bxc.bx-campaign-1578374.bx-active-step-1 .bx-creative:before {min-height: 40px;}.bxc.bx-campaign-1578374.bx-active-step-1 .bx-close {top: 0;right: 0;}.bxc.bx-campaign-1578374 .bx-group-1578374-UL1Kmfv {width: auto;padding: 0;}.bxc.bx-campaign-1578374 .bx-element-1578374-HEFBD6M {width: auto;}.bxc.bx-campaign-1578374 .bx-element-1578374-HEFBD6M> *:first-child {font-size: 14px;}"
        }
    };
    bouncex.creatives = false;
    bouncex.brandStyles = {
        "31517": "\/* effects for .bx-campaign-31517 *\/\/* custom css .bx-campaign-31517 *\/\/************************************ BRAND STYLE STRUCTURE V4.0Do not remove or edit.************************************\/\/***LOGO***\/\/*Logo setting - MUST use img and not :first-child. <img> gets wrapped in <h2> in custom code *\/.bx-custom.bx-brand-31517 .bx-group .bx-row-image-logo > img, .bx-custom.bx-campaign-31517 .bx-group .bx-row-image-logo > img {    height: 40px;    width: auto;}\/* Loading Icon F (FLIP) *\/.bx-custom.bx-brand-31517 .bx-loader-flip > div,.bx-custom.bx-campaign-31517 .bx-loader-flip > div {    background-color: #e01a2b;    height: 30px;    width: 30px;    -webkit-animation: bx-anim-31517-loader-flip 1.2s infinite ease-in-out;    animation: bx-anim-31517-loader-flip 1.2s infinite ease-in-out;}@-webkit-keyframes bx-anim-31517-loader-flip {  0% { -webkit-transform: perspective(120px) }  50% { -webkit-transform: perspective(120px) rotateY(180deg) }  100% { -webkit-transform: perspective(120px) rotateY(180deg)  rotateX(180deg) }}@keyframes bx-anim-31517-loader-flip {  0% {     transform: perspective(120px) rotateX(0deg) rotateY(0deg);  }   50% {     transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);  }   100% {     transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);  }}\/***TIMER***\/\/*Units*\/.bx-custom.bx-brand-31517 .bx-timer-units, .bx-custom.bx-campaign-31517 .bx-timer-units {    min-width: 2.5em;    display:inline-block;    position: relative;}.bx-custom.bx-brand-31517 .bx-timer-units:after, .bx-custom.bx-campaign-31517 .bx-timer-units:after {    padding: 0 0 0 0.25em;}.bx-custom.bx-brand-31517 .bx-timer-units.bx-timer-days:after, .bx-custom.bx-campaign-31517 .bx-timer-units.bx-timer-days:after {  content: 'D';}.bx-custom.bx-brand-31517 .bx-timer-units.bx-timer-hours:after, .bx-custom.bx-campaign-31517 .bx-timer-units.bx-timer-hours:after {  content: 'H';}.bx-custom.bx-brand-31517 .bx-timer-units.bx-timer-minutes:after, .bx-custom.bx-campaign-31517 .bx-timer-units.bx-timer-minutes:after {  content: 'M';}.bx-custom.bx-brand-31517 .bx-timer-units.bx-timer-seconds:after, .bx-custom.bx-campaign-31517 .bx-timer-units.bx-timer-seconds:after {  content: 'S';}\/***CHECKBOX & RADIO***\/\/*Checkbox default*\/.bx-custom.bx-brand-31517 .bx-row-checkbox .bx-checkelem + .bx-checkshape,.bx-custom.bx-campaign-31517 .bx-row-checkbox .bx-checkelem + .bx-checkshape,.bx-custom.bx-brand-31517 .bx-row-radio .bx-radioelem + .bx-radioshape,.bx-custom.bx-campaign-31517 .bx-row-radio .bx-radioelem + .bx-radioshape {  flex: 0 0 auto;}.bx-custom.bx-brand-31517 .bx-row-checkbox .bx-label,.bx-custom.bx-campaign-31517 .bx-row-checkbox .bx-label,.bx-custom.bx-brand-31517 .bx-row-radio .bx-label,.bx-custom.bx-campaign-31517 .bx-row-radio .bx-label {    display: inline-flex;}\/***INPUT***\/\/*Shrink to top label fix*\/.bx-custom.bx-brand-31517 .bx-row-placeholder-top:not(.bx-row-input-label) input,.bx-custom.bx-campaign-31517 .bx-row-placeholder-top:not(.bx-row-input-label) input{    min-height: 2em !important;}\/*Persistent label*\/.bx-custom.bx-brand-31517 .bx-row-input.bx-row-input-label .bx-placeholder,.bx-custom.bx-campaign-31517 .bx-row-input.bx-row-input-label .bx-placeholder {    left: 0;    opacity: 1;    position: relative;}.bx-custom.bx-brand-31517 .bx-row-input.bx-row-input-label .bx-placeholder .bx-placeholdertext,.bx-custom.bx-campaign-31517 .bx-row-input.bx-row-input-label .bx-placeholder .bx-placeholdertext {    bottom: calc(100% + 1em);    color: white;    font-family: \"Macys Sans\", sans-serif;    font-size: 14px;    font-weight: 700;    left: 0;    position: absolute;    text-align: left;    text-transform: none;}\/***SELECT MENU***\/\/*Select (dropdown) arrows*\/.bx-custom.bx-brand-31517 .bx-row-select .bx-select:after,.bx-custom.bx-campaign-31517 .bx-row-select .bx-select:after {  color: inherit;}\/*** VALIDATION STYLES ***\/\/* Borders on input & select elements*\/.bx-custom.bx-brand-31517 .bx-row-input.bx-row-validation .bx-input,.bx-custom.bx-campaign-31517 .bx-row-input.bx-row-validation .bx-input,.bx-custom.bx-brand-31517 .bx-row-select.bx-row-validation .bx-select,.bx-custom.bx-campaign-31517 .bx-row-select.bx-row-validation .bx-select {    border-color: white; \/*Specify border color*\/}.bx-custom.bx-brand-31517 .bx-row-input.bx-row-validation .bx-input:focus,.bx-custom.bx-campaign-31517 .bx-row-input.bx-row-validation .bx-input:focus, .bx-custom.bx-brand-31517 .bx-row-select.bx-row-validation.bx-has-focus .bx-select,.bx-custom.bx-campaign-31517 .bx-row-select.bx-row-validation.bx-has-focus .bx-select {    border-color: white; \/*Specify border color on focus*\/}\/***FOCUS STYLES***\/\/* Note: These rules can be broken out to create separate focus sytles for different elementsEx: Text inputs could change background-color on focus, but radio buttons could recieve a styled outline*\/.bxc.bx-campaign-31517.bx-ally form:focus,.bxc.bx-campaign-31517.bx-ally a:focus,.bxc.bx-campaign-31517.bx-ally button:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-submit .bx-button:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=\"\"]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=email]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=password]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=tel]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=text]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=url]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=color]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=date]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=datetime]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=datetime-local]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=month]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=time]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=week]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=number]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-input [type=search]:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-textarea .bx-textarea:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-select .bx-selectelem:focus,.bxc.bx-campaign-31517.bx-ally .bx-row-checkbox .bx-checkelem:focus + .bx-checkshape,.bxc.bx-campaign-31517.bx-ally .bx-row-radio .bx-radioelem:focus + .bx-radioshape,.bxc.bx-brand-31517.bx-ally form:focus,.bxc.bx-brand-31517.bx-ally a:focus,.bxc.bx-brand-31517.bx-ally button:focus,.bxc.bx-brand-31517.bx-ally .bx-row-submit .bx-button:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=\"\"]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=email]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=password]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=tel]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=text]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=url]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=color]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=date]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=datetime]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=datetime-local]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=month]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=time]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=week]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=number]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-input [type=search]:focus,.bxc.bx-brand-31517.bx-ally .bx-row-textarea .bx-textarea:focus,.bxc.bx-brand-31517.bx-ally .bx-row-select .bx-selectelem:focus,.bxc.bx-brand-31517.bx-ally .bx-row-checkbox .bx-checkelem:focus + .bx-checkshape,.bxc.bx-brand-31517.bx-ally .bx-row-radio .bx-radioelem:focus + .bx-radioshape {    outline: 3px solid #025FCC;    box-shadow: 0 0 0px 6px #fff;    outline-offset: 0px;}\/***STRUCTURE***\/\/*General*\/.bx-custom.bx-brand-31517 .bx-row,.bx-custom.bx-brand-31517 .bx-group, .bx-custom.bx-campaign-31517 .bx-row,.bx-custom.bx-campaign-31517 .bx-group {  max-width: 100%;}.bx-custom.bx-brand-31517 .bx-row > a,.bx-custom.bx-campaign-31517 .bx-row > a {   display: block;}.bx-custom.bx-brand-31517 .bx-row-image, .bx-custom.bx-campaign-31517 .bx-row-image,.bx-custom.bx-brand-31517 .bx-row-line-logo, .bx-custom.bx-campaign-31517 .bx-row-line-logo {  padding: 0;}\/***VX ADDITIONS - COPY PASTE TO OLD BRAND STYLE***\/\/*Firefox fix*\/.bx-custom.bx-brand-31517 .bx-el::-moz-placeholder,.bx-custom.bx-campaign-31517 .bx-el::-moz-placeholder {    opacity: 1;}\/*Font smoothing fix*\/.bx-custom.bx-brand-31517 .bx-creative,.bx-custom.bx-campaign-31517 .bx-creative {  -webkit-font-smoothing: auto;  -moz-osx-font-smoothing: auto;  -moz-text-size-adjust: none;}\/*Lingering buttons fix*\/.bx-custom.bx-brand-31517 a,.bx-custom.bx-campaign-31517 a {    transition: none;}\/* Prevent links becoming large when text is small on phones*\/.bx-custom.bx-brand-31517 .bx-row *,.bx-custom.bx-campaign-31517  .bx-row * {    -webkit-text-size-adjust: 100%;}\/* Focus outline adjustment *\/.bxc.bx-custom.bx-brand-31517 .bx-2-heading,.bxc.bx-custom.bx-campaign-31517 .bx-2-heading {    display: inline-block;    max-width: 100%;}.bxc.bx-custom.bx-brand-31517 .bx-2-heading[tabindex=\"-1\"]:focus,.bxc.bx-custom.bx-campaign-31517 .bx-2-heading[tabindex=\"-1\"]:focus {    outline: 3px solid transparent;}.bxc.bx-brand-31517 .bx-creative> *:first-child {width: 400px;}.bxc.bx-brand-31517 .bx-creative:before {min-height: 480px;}.bxc.bx-brand-31517 .bx-creative {background-color: black;}.bxc.bx-brand-31517 .bx-shroud {background-size: cover;}.bxc.bx-brand-31517 .bx-close {width: 48px;top: 0;right: 0;height: 48px;padding: 12px;border-radius: 0;stroke-width: 1px;stroke: white;}.bxc.bx-brand-31517 .bx-row-image-logo {padding: 0;width: auto;}.bxc.bx-brand-31517 .bx-row-image-logo> *:first-child {background-color: transparent;background-size: contain;padding: 0;}.bxc.bx-brand-31517 .bx-row-html-default {width: auto;text-align: center;}.bxc.bx-brand-31517 .bx-row-text-custom> *:first-child {font-size: 25px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: white;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-text-custom {padding: 0;}.bxc.bx-brand-31517 .bx-row-text-subheadline> *:first-child {font-size: 14px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 500;line-height: 1.43em;color: white;}.bxc.bx-brand-31517 .bx-row-text-subheadline {padding: 0;}.bxc.bx-brand-31517 .bx-row-text-headline> *:first-child {font-size: 36px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: white;line-height: 1.1;}.bxc.bx-brand-31517 .bx-row-text-headline {padding: 0;}.bxc.bx-brand-31517 .bx-row-text-default> *:first-child {font-size: 14px;padding: 0;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;line-height: 1.43em;color: white;}.bxc.bx-brand-31517 .bx-row-text-default {padding: 0;}.bxc.bx-brand-31517 .bx-row-timer-default {width: auto;padding: 0;}.bxc.bx-brand-31517 .bx-row-timer-default> *:first-child {padding: 0;border-style: none;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: white;}.bxc.bx-brand-31517 .bx-row-coupon-default {width: auto;padding: 0;}.bxc.bx-brand-31517 .bx-row-coupon-default> *:first-child {padding: 0;border-style: solid;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;color: white;}.bxc.bx-brand-31517 .bx-row-checkbox-GDPR {padding: 0;text-align: left;}.bxc.bx-brand-31517 .bx-row-checkbox-GDPR> *:first-child {line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;color: white;}.bxc.bx-brand-31517 .bx-row-checkbox-GDPR .bx-checkelem+.bx-checkshape.bx-component {background-color: white;width: 18px;height: 18px;margin-right: 8px;border-radius: 0;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31517 .bx-row-checkbox-GDPR .bx-checkelem:checked+.bx-checkshape.bx-component.bx-component {background-color: white;box-shadow: none;stroke: #000000;}.bxc.bx-brand-31517 .bx-row-checkbox-GDPR .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-checkbox-GDPR .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-checkbox-default {padding: 0;text-align: left;}.bxc.bx-brand-31517 .bx-row-checkbox-default> *:first-child {line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;color: white;}.bxc.bx-brand-31517 .bx-row-checkbox-default .bx-checkelem+.bx-checkshape.bx-component {background-color: white;width: 18px;height: 18px;margin-right: 8px;border-radius: 0;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31517 .bx-row-checkbox-default .bx-checkelem:checked+.bx-checkshape.bx-component.bx-component {background-color: white;box-shadow: none;stroke: #000000;}.bxc.bx-brand-31517 .bx-row-checkbox-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-checkbox-default .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-radio-default {padding: 0;text-align: left;}.bxc.bx-brand-31517 .bx-row-radio-default> *:first-child {padding: 0;line-height: 1.25;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: white;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-radio-default .bx-radioelem+.bx-radioshape.bx-component {background-color: white;width: 18px;height: 18px;margin-right: 8px;box-shadow: none;text-shadow: none;border-width: 1px;border-style: solid;border-color: #c0c0c0;}.bxc.bx-brand-31517 .bx-row-radio-default .bx-radioelem:checked+.bx-radioshape.bx-component.bx-component {background-color: white;box-shadow: none;fill: #000000;}.bxc.bx-brand-31517 .bx-row-radio-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-radio-default .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el {font-size: 16px;text-align: left;background-color: white;border-style: solid;border-color: #c0c0c0;border-width: 1px;padding: 11.5px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el:focus {background-color: white;color: #000000;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el::-webkit-input-placeholder {color: black;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el:-moz-placeholder {color: black;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el::-moz-placeholder {color: black;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el:-ms-input-placeholder {color: black;}.bxc.bx-brand-31517 .bx-row-input-default .bx-el {padding: 11.5px;}.bxc.bx-brand-31517 .bx-row-input-default {padding: 5px;text-align: left;}.bxc.bx-brand-31517 .bx-row-input-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-input-default .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el {padding: 11.5px;font-size: 16px;text-align: left;background-color: white;border-style: solid;border-color: #c0c0c0;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: black;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el:focus {background-color: white;color: #000000;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el::-webkit-input-placeholder {color: transparent;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el:-moz-placeholder {color: transparent;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el::-moz-placeholder {color: transparent;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el:-ms-input-placeholder {color: transparent;}.bxc.bx-brand-31517 .bx-row-input-label .bx-el {padding: 11.5px;}.bxc.bx-brand-31517 .bx-row-input-label {padding: 15px 5px 5px 5px;}.bxc.bx-brand-31517 .bx-row-input-label .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-input-label .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-select-default {padding: 5px;}.bxc.bx-brand-31517 .bx-row-select-default> *:first-child {padding: 12.5px;font-size: 16px;line-height: 1.2;border-color: #c0c0c0;background-color: white;color: black;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-select-default> *:first-child:focus {background-color: white;color: #000000;}.bxc.bx-brand-31517 .bx-row-select-default .bx-component-validation {padding: 5px 0 0;position: static;font-size: 12px;color: white;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-has-focus.bx-row-select-default .bx-component-validation {color: white;}.bxc.bx-brand-31517 .bx-row-submit-default> *:first-child {padding: 13px;font-size: 16px;background-color: #e01a2b;border-style: solid;border-color: #e01a2b;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;transition: background-color .25s ease-out,color .25s ease-out;}.bxc.bx-brand-31517 .bx-row-submit-default> *:first-child:hover {border-color: #ab0000;background-color: #ab0000;}.bxc.bx-brand-31517 .bx-row-submit-default {padding: 5px;}.bxc.bx-brand-31517 .bx-row-submit-custom> *:first-child {background-color: transparent;padding: 13px;font-size: 16px;color: white;border-style: solid;border-width: 1px;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 700;}.bxc.bx-brand-31517 .bx-row-submit-custom {padding: 5px;}.bxc.bx-brand-31517 .bx-row-submit-no> *:first-child {background-color: transparent;padding: 0;font-size: 14px;color: white;text-decoration: underline;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;font-weight: 400;}.bxc.bx-brand-31517 .bx-row-submit-no {padding: 0;width: auto;}.bxc.bx-brand-31517 .bx-row-text-link {padding: 0;width: auto;}.bxc.bx-brand-31517 .bx-row-text-link> *:first-child {padding: 0;font-size: 12px;font-weight: 400;cursor: pointer;text-decoration: underline;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: white;}.bxc.bx-brand-31517 .bx-row-text-link> *:first-child:hover {-webkit-transition: color .15s ease-in-out;transition: color .15s ease-in-out;}.bxc.bx-brand-31517 .bx-row-text-sosumi {width: auto;padding: 0;}.bxc.bx-brand-31517 .bx-row-text-sosumi> *:first-child {padding: 0;font-size: 12px;font-weight: 400;font-family: \"Macys Sans\",Helvetica Neue,Helvetica,Arial,sans-serif;color: white;}"
    };
    bouncex.webfonts = false;

    if (bouncex.gbi) {
        bouncex.gbi.stacks = false;
    }

    var newCookie = {
        "swv": false,
        "did": "1421790325972522681",
        "vid": 1741352709216821,
        "v": {
            "ever_logged_in": false,
            "cart_items": "false",
            "cart": false,
            "cart_items_qty": "false",
            "cart_items_offset": "false",
            "cart_set": "false",
            "logged_in_identified": "false",
            "welcome_code": false,
            "starrewards_login_ever": false,
            "quickreg_submit": "false",
            "quickreg_submit_ever": false,
            "is_subscribed": false,
            "coupon_code_url": "false",
            "sfl_item_ids": false
        },
        "pdFirstLoad": true,
        "dg": {
            "isPreviousCustomer": false,
            "isSubscriber": false,
            "isTextSubscriber": false,
            "cache_ts": 1741352739474
        },
        "fvt": 1741352709,
        "ao": 1,
        "lp": "https%3A%2F%2Fwww.macys.com%2Fshop%2Fsale%2Fmacys-deals-and-coupons%3Fid%3D334356",
        "as": 1,
        "vpv": 1,
        "d": "d",
        "r": "",
        "cvt": 1741352709,
        "sid": 13,
        "gcr": 43,
        "m": 0,
        "lvt": 1741352711,
        "campaigns": {
            "2504550": {
                "vv": 1,
                "lvt": 1741352709,
                "lavid": 1741352709216821,
                "la": 1741352709,
                "av": 1,
                "fsa": 1741352709,
                "as": 1,
                "ao": 1,
                "oa": []
            },
            "2504554": {
                "vv": 1,
                "lvt": 1741352709
            },
            "2534932": {
                "vv": 1,
                "lvt": 1741352709
            },
            "2534938": {
                "vv": 1,
                "lvt": 1741352709
            },
            "2846244": {
                "vv": 1,
                "lvt": 1741352711,
                "lavid": 1741352709216821,
                "la": 1741352711,
                "av": 1,
                "fsa": 1741352711,
                "as": 1,
                "ao": 1,
                "oa": [1741352711],
                "io": 1,
                "lclk": 1741352714,
                "ls": 1741352744,
                "wcv": 1741352991,
                "wc": 1741352991
            },
            "1578374": {
                "vv": 0,
                "lvt": 1741352993
            }
        },
        "uid": 1,
        "es": true
    };
    var campaignAdded = false;
    for (var campaignId in newCampaigns) {
        if (newCampaigns.hasOwnProperty(campaignId)) {
            // if campaign cookie does not exist
            if (!bouncex.cookie.campaigns) {
                bouncex.cookie.campaigns = {};
            } else {
                bouncex.cookie.campaigns = Object.assign({}, bouncex.cookie.campaigns);
            }

            if (!bouncex.cookie.campaigns[campaignId]) {
                campaignAdded = true;
                bouncex.cookie.campaigns[campaignId] = {
                    lvt: bouncex.cookie.lvt,
                    vv: 0
                };
            } else if (newCookie.campaigns[campaignId]) {
                // need to set campaign cookie's activations_sessions to the new cookie as it gets modified in reloadCampaigns
                campaignAdded = true;
                bouncex.cookie.campaigns[campaignId].as = newCookie.campaigns[campaignId].as;
            }
        }
    }
    if (campaignAdded) {
        bouncex.setBounceCookie();
    }

    for (var campaignId in bouncex.campaigns) {
        if (bouncex.campaigns.hasOwnProperty(campaignId)) {
            //copy state vars
            if (newCampaigns[campaignId]) {
                newCampaigns[campaignId].ap = bouncex.campaigns[campaignId].ap;
                newCampaigns[campaignId].repressed = Boolean(bouncex.campaigns[campaignId].repressed);
            }

            if (newCampaigns[campaignId] && bouncex.campaigns[campaignId].ad_visible && newCampaigns[campaignId].html.replace(/fsa=(\d+)&|width=(\d+)&|height=(\d+)&/gi, '') == bouncex.campaigns[campaignId].html.replace(/fsa=(\d+)&|width=(\d+)&|height=(\d+)&/gi, '')) {
                newCampaigns[campaignId] = bouncex.campaigns[campaignId];
            } else if (newCampaigns[campaignId] && bouncex.isGbi2Campaign(campaignId) && bouncex.campaigns[campaignId].gbi.hasBegunAuction) {
                newCampaigns[campaignId] = bouncex.campaigns[campaignId];
            } else {
                bouncex.destroy_ad(campaignId);
            }
        }
    }

    bouncex.campaigns = newCampaigns;
    newCampaigns = {};

    bouncex.debug = false;
    bouncex.setBounceCookie();
    if (bouncex.campaigns) {
        for (var campaignId in bouncex.campaigns) {
            if (bouncex.campaigns[campaignId].ad_visible && typeof bouncex.repressCampaigns === 'function') {
                bouncex.repressCampaigns(campaignId);
            }
        }
        bouncex.loadBounceCss(bouncex.initActivationFuncs);
    }
    bouncex.loadBrandStyles();
    bouncex.loadWebfonts();

})();
