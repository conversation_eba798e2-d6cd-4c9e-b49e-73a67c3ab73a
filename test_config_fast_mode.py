"""
测试config.py中的快速模式配置
"""

from playwright_framework import PlayWrightFrameWork
from tool import Log
import config
import time


class ConfigFastModeTest(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log("config_fast_mode_test.log", debug=True))
    
    def ok(self):
        return True
    
    def test_config_setting(self):
        """测试配置设置"""
        page = self.page
        ghost = self.ghost_cursor
        
        # 创建测试页面
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>配置测试</title>
            <style>
                body { padding: 50px; font-family: Arial, sans-serif; }
                .info { margin: 20px; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
                .target { 
                    width: 150px; 
                    height: 60px; 
                    margin: 20px; 
                    padding: 10px; 
                    background: #007bff; 
                    color: white; 
                    border: none; 
                    cursor: pointer;
                    border-radius: 5px;
                    font-size: 16px;
                }
                .target:hover { background: #0056b3; }
                .clicked { background: #28a745 !important; }
            </style>
        </head>
        <body>
            <h1>GhostCursor配置测试</h1>
            
            <div class="info">
                <h3>当前配置信息</h3>
                <p><strong>快速模式配置:</strong> <span id="config-value"></span></p>
                <p><strong>实际快速模式:</strong> <span id="actual-mode"></span></p>
            </div>
            
            <button class="target" id="test-btn" onclick="testClick()">点击测试按钮</button>
            
            <div class="info" id="result">等待测试...</div>
            
            <script>
                // 显示配置信息
                document.getElementById('config-value').textContent = 'ghost_cursor_fast_mode = True';
                document.getElementById('actual-mode').textContent = '从日志中查看';
                
                function testClick() {
                    document.getElementById('test-btn').classList.add('clicked');
                    const now = new Date().toLocaleTimeString();
                    document.getElementById('result').innerHTML = 
                        '<h4>测试结果</h4><p>按钮被点击！时间: ' + now + '</p>';
                }
            </script>
        </body>
        </html>
        """
        
        try:
            self.log.notice("=== 配置测试开始 ===")
            
            # 显示当前配置
            fast_mode_config = getattr(config, 'ghost_cursor_fast_mode', '未设置')
            self.log.notice(f"config.py中的配置: ghost_cursor_fast_mode = {fast_mode_config}")
            
            # 显示GhostCursor实际状态
            if hasattr(ghost, 'fast_mode'):
                self.log.notice(f"GhostCursor实际快速模式: {ghost.fast_mode}")
            else:
                self.log.err("GhostCursor没有fast_mode属性")
            
            # 设置测试页面
            page.set_content(html_content)
            time.sleep(1)
            
            # 测试点击性能
            self.log.notice("开始点击性能测试...")
            
            start_time = time.time()
            success = ghost.safe_click_element('#test-btn')
            end_time = time.time()
            
            click_duration = end_time - start_time
            self.log.success(f"点击完成，耗时: {click_duration:.3f}秒，成功: {success}")
            
            # 保存测试结果
            with open("config_test_result.log", "w", encoding="utf-8") as f:
                f.write("=== GhostCursor配置测试结果 ===\n")
                f.write(f"配置文件设置: ghost_cursor_fast_mode = {fast_mode_config}\n")
                f.write(f"实际快速模式: {getattr(ghost, 'fast_mode', '未知')}\n")
                f.write(f"点击耗时: {click_duration:.3f}秒\n")
                f.write(f"点击成功: {success}\n")
                f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 截图
            page.screenshot(path="config_test_result.png")
            self.log.notice("测试结果已保存")
            
            return click_duration
            
        except Exception as e:
            self.log.err(f"配置测试出错: {e}")
            return None
    
    def test_different_modes(self):
        """测试不同模式的性能差异"""
        try:
            self.log.notice("=== 模式性能对比 ===")
            
            # 当前模式测试
            current_duration = self.test_config_setting()
            
            if current_duration:
                current_mode = getattr(self.ghost_cursor, 'fast_mode', '未知')
                self.log.notice(f"当前模式 ({current_mode}) 耗时: {current_duration:.3f}秒")
                
                # 性能评估
                if current_mode:
                    if current_duration < 0.5:
                        self.log.success("✅ 快速模式性能优秀")
                    elif current_duration < 1.0:
                        self.log.notice("⚡ 快速模式性能良好")
                    else:
                        self.log.err("❌ 快速模式性能不佳")
                else:
                    if current_duration < 2.0:
                        self.log.success("✅ 自然模式性能正常")
                    else:
                        self.log.err("❌ 自然模式性能较慢")
            
        except Exception as e:
            self.log.err(f"性能对比测试出错: {e}")
    
    def worker(self):
        """主要工作函数"""
        try:
            self.log.notice("开始配置测试...")
            
            # 测试1: 配置设置
            self.test_config_setting()
            
            # 测试2: 性能对比
            self.test_different_modes()
            
            self.log.success("配置测试完成！")
            
            # 保持浏览器打开
            self.log.notice("保持浏览器打开10秒...")
            time.sleep(10)
            
        except Exception as e:
            self.log.err(f"测试出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=== GhostCursor配置测试 ===")
    print("测试config.py中的快速模式配置")
    print("=" * 40)
    
    # 显示当前配置
    fast_mode_setting = getattr(config, 'ghost_cursor_fast_mode', '未设置')
    print(f"当前配置: ghost_cursor_fast_mode = {fast_mode_setting}")
    print()
    
    try:
        with ConfigFastModeTest() as test:
            if test.new_chrome_browser():
                test.worker()
            else:
                print("浏览器启动失败")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
