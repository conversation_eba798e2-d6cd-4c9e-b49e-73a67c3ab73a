"""
测试GhostCursor启用/禁用配置
"""

from playwright_framework import PlayWrightFrameWork
from tool import Log
import config
import time


class GhostCursorConfigTest(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log("ghost_cursor_config_test.log", debug=True))
    
    def ok(self):
        return True
    
    def test_ghost_cursor_status(self):
        """测试GhostCursor状态"""
        page = self.page
        
        # 创建测试页面
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>GhostCursor配置测试</title>
            <style>
                body { padding: 50px; font-family: Arial, sans-serif; }
                .info { margin: 20px; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
                .config { margin: 20px; padding: 15px; background: #e9ecef; border-radius: 5px; }
                .target { 
                    width: 200px; 
                    height: 80px; 
                    margin: 20px; 
                    padding: 15px; 
                    background: #007bff; 
                    color: white; 
                    border: none; 
                    cursor: pointer;
                    border-radius: 8px;
                    font-size: 18px;
                    display: block;
                }
                .target:hover { background: #0056b3; }
                .clicked { background: #28a745 !important; }
                .status { margin: 20px; padding: 15px; border-radius: 5px; }
                .enabled { background: #d4edda; border-left: 4px solid #28a745; }
                .disabled { background: #f8d7da; border-left: 4px solid #dc3545; }
            </style>
        </head>
        <body>
            <h1>GhostCursor配置测试</h1>
            
            <div class="config">
                <h3>当前配置</h3>
                <p><strong>ghost_cursor_enabled:</strong> <span id="enabled-config"></span></p>
                <p><strong>ghost_cursor_fast_mode:</strong> <span id="fast-mode-config"></span></p>
            </div>
            
            <div class="status" id="ghost-status">
                <h3>GhostCursor状态</h3>
                <p id="status-text">检测中...</p>
            </div>
            
            <button class="target" id="test-btn" onclick="testClick()">点击测试按钮</button>
            
            <div class="info" id="result">等待测试...</div>
            
            <script>
                // 显示配置信息
                document.getElementById('enabled-config').textContent = 'True';
                document.getElementById('fast-mode-config').textContent = 'True';
                
                function testClick() {
                    document.getElementById('test-btn').classList.add('clicked');
                    const now = new Date().toLocaleTimeString();
                    document.getElementById('result').innerHTML = 
                        '<h4>测试结果</h4>' +
                        '<p>按钮被点击！时间: ' + now + '</p>' +
                        '<p>请查看日志了解使用的点击方法</p>';
                }
            </script>
        </body>
        </html>
        """
        
        try:
            self.log.notice("=== GhostCursor配置测试开始 ===")
            
            # 显示当前配置
            enabled_config = getattr(config, 'ghost_cursor_enabled', '未设置')
            fast_mode_config = getattr(config, 'ghost_cursor_fast_mode', '未设置')
            
            self.log.notice(f"配置文件设置:")
            self.log.notice(f"  ghost_cursor_enabled = {enabled_config}")
            self.log.notice(f"  ghost_cursor_fast_mode = {fast_mode_config}")
            
            # 检查实际状态
            if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                self.log.success("✅ GhostCursor已启用")
                fast_mode = getattr(self.ghost_cursor, 'fast_mode', '未知')
                self.log.notice(f"  快速模式: {fast_mode}")
                ghost_status = "enabled"
                status_text = f"GhostCursor已启用 (快速模式: {fast_mode})"
            else:
                self.log.notice("❌ GhostCursor已禁用")
                ghost_status = "disabled"
                status_text = "GhostCursor已禁用，将使用原生点击"
            
            # 设置测试页面
            page.set_content(html_content)
            time.sleep(1)
            
            # 更新页面状态显示
            page.evaluate(f"""
                document.getElementById('enabled-config').textContent = '{enabled_config}';
                document.getElementById('fast-mode-config').textContent = '{fast_mode_config}';
                document.getElementById('status-text').textContent = '{status_text}';
                document.getElementById('ghost-status').className = 'status {ghost_status}';
            """)
            
            # 测试点击
            self.log.notice("开始点击测试...")
            
            start_time = time.time()
            if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                # 使用GhostCursor点击
                self.log.debug("使用GhostCursor进行点击")
                success = self.ghost_cursor.safe_click_element('#test-btn')
                click_method = "GhostCursor"
            else:
                # 使用原生点击
                self.log.debug("使用原生点击")
                page.click('#test-btn')
                success = True
                click_method = "原生点击"
            
            end_time = time.time()
            click_duration = end_time - start_time
            
            self.log.success(f"点击完成:")
            self.log.notice(f"  方法: {click_method}")
            self.log.notice(f"  成功: {success}")
            self.log.notice(f"  耗时: {click_duration:.3f}秒")
            
            # 保存测试结果
            with open("ghost_cursor_config_test_result.log", "w", encoding="utf-8") as f:
                f.write("=== GhostCursor配置测试结果 ===\n")
                f.write(f"配置文件设置:\n")
                f.write(f"  ghost_cursor_enabled = {enabled_config}\n")
                f.write(f"  ghost_cursor_fast_mode = {fast_mode_config}\n")
                f.write(f"\n实际状态:\n")
                f.write(f"  GhostCursor状态: {'启用' if ghost_status == 'enabled' else '禁用'}\n")
                if ghost_status == 'enabled':
                    f.write(f"  快速模式: {getattr(self.ghost_cursor, 'fast_mode', '未知')}\n")
                f.write(f"\n测试结果:\n")
                f.write(f"  点击方法: {click_method}\n")
                f.write(f"  点击成功: {success}\n")
                f.write(f"  点击耗时: {click_duration:.3f}秒\n")
                f.write(f"  测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 截图
            page.screenshot(path="ghost_cursor_config_test.png")
            self.log.notice("测试结果已保存")
            
            return success
            
        except Exception as e:
            self.log.err(f"配置测试出错: {e}")
            return False
    
    def worker(self):
        """主要工作函数"""
        try:
            self.log.notice("开始GhostCursor配置测试...")
            
            success = self.test_ghost_cursor_status()
            
            if success:
                self.log.success("🎉 GhostCursor配置测试通过！")
            else:
                self.log.err("❌ GhostCursor配置测试失败！")
            
            # 保持浏览器打开
            self.log.notice("保持浏览器打开10秒...")
            time.sleep(10)
            
        except Exception as e:
            self.log.err(f"测试出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=== GhostCursor配置测试 ===")
    print("测试config.py中的GhostCursor启用/禁用配置")
    print("=" * 40)
    
    # 显示当前配置
    enabled_setting = getattr(config, 'ghost_cursor_enabled', '未设置')
    fast_mode_setting = getattr(config, 'ghost_cursor_fast_mode', '未设置')
    
    print(f"当前配置:")
    print(f"  ghost_cursor_enabled = {enabled_setting}")
    print(f"  ghost_cursor_fast_mode = {fast_mode_setting}")
    print()
    
    try:
        with GhostCursorConfigTest() as test:
            if test.new_chrome_browser():
                test.worker()
            else:
                print("浏览器启动失败")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
