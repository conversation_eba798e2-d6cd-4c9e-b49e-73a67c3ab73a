"""
GhostCursor配置切换工具
用于在config.py中切换GhostCursor相关配置
"""

import re
import os


def read_config():
    """读取当前配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找GhostCursor启用配置
        enabled_match = re.search(r'ghost_cursor_enabled\s*=\s*(True|False)', content)
        enabled = enabled_match.group(1) == 'True' if enabled_match else True

        # 查找快速模式配置
        fast_match = re.search(r'ghost_cursor_fast_mode\s*=\s*(True|False)', content)
        fast_mode = fast_match.group(1) == 'True' if fast_match else True

        return {
            'enabled': enabled,
            'fast_mode': fast_mode
        }
    except Exception as e:
        print(f"读取配置文件失败: {e}")
        return None


def write_config(enabled=None, fast_mode=None):
    """写入新配置"""
    try:
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换启用配置
        if enabled is not None:
            enabled_value = 'True' if enabled else 'False'
            pattern = r'(ghost_cursor_enabled\s*=\s*)(True|False)'
            replacement = f'\\g<1>{enabled_value}'
            content = re.sub(pattern, replacement, content)
            print(f"✅ GhostCursor启用状态已更新: {enabled_value}")

        # 替换快速模式配置
        if fast_mode is not None:
            fast_value = 'True' if fast_mode else 'False'
            pattern = r'(ghost_cursor_fast_mode\s*=\s*)(True|False)'
            replacement = f'\\g<1>{fast_value}'
            content = re.sub(pattern, replacement, content)
            print(f"✅ 快速模式已更新: {fast_value}")

        # 写回文件
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(content)

        return True

    except Exception as e:
        print(f"写入配置文件失败: {e}")
        return False


def show_current_config():
    """显示当前配置"""
    current = read_config()
    if current is not None:
        mode_text = "快速模式" if current else "自然模式"
        print(f"当前配置: ghost_cursor_fast_mode = {current} ({mode_text})")
    else:
        print("无法读取当前配置")


def toggle_fast_mode():
    """切换快速模式"""
    current = read_config()
    if current is not None:
        new_mode = not current
        if write_config(new_mode):
            mode_text = "快速模式" if new_mode else "自然模式"
            print(f"🔄 已切换到: {mode_text}")
        else:
            print("❌ 切换失败")
    else:
        print("❌ 无法读取当前配置，切换失败")


def set_fast_mode(enable):
    """设置快速模式"""
    if write_config(enable):
        mode_text = "快速模式" if enable else "自然模式"
        print(f"🎯 已设置为: {mode_text}")
    else:
        print("❌ 设置失败")


def main():
    """主函数"""
    print("=== GhostCursor快速模式配置工具 ===")
    print()
    
    # 显示当前配置
    show_current_config()
    print()
    
    while True:
        print("请选择操作:")
        print("1. 启用快速模式 (推荐)")
        print("2. 启用自然模式")
        print("3. 切换模式")
        print("4. 查看当前配置")
        print("5. 退出")
        print()
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            print()
            
            if choice == '1':
                set_fast_mode(True)
                print("💡 快速模式特点:")
                print("   - 鼠标移动速度快 (0.05-0.3秒)")
                print("   - 减少路径点数和延时")
                print("   - 适合自动化任务")
                
            elif choice == '2':
                set_fast_mode(False)
                print("💡 自然模式特点:")
                print("   - 鼠标移动更自然 (0.1-0.8秒)")
                print("   - 更多路径点和随机延时")
                print("   - 更难被检测为机器人")
                
            elif choice == '3':
                toggle_fast_mode()
                
            elif choice == '4':
                show_current_config()
                
            elif choice == '5':
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请输入1-5")
            
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作出错: {e}")


if __name__ == "__main__":
    main()
