"""
简单的GhostCursor测试
专门测试修复后的功能
"""

from playwright_framework import PlayWrightFrameWork
from tool import Log
import time


class SimpleGhostTest(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log("simple_test.log", debug=True))
    
    def ok(self):
        return True
    
    def test_basic_functionality(self):
        """测试基本功能"""
        page = self.page
        ghost = self.ghost_cursor
        
        # 创建一个非常简单的测试页面
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>简单测试</title>
            <style>
                body { padding: 50px; font-family: Arial, sans-serif; }
                button { 
                    padding: 15px 30px; 
                    font-size: 18px; 
                    margin: 20px; 
                    cursor: pointer;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 5px;
                }
                input { 
                    padding: 10px; 
                    font-size: 16px; 
                    width: 300px; 
                    margin: 20px;
                    border: 2px solid #ccc;
                    border-radius: 5px;
                }
                .result { 
                    margin: 20px; 
                    padding: 15px; 
                    background: #f8f9fa; 
                    border-left: 4px solid #007bff;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <h1>GhostCursor 简单测试</h1>
            
            <button id="simple-btn">简单按钮</button>
            <div id="btn-result" class="result">等待按钮点击...</div>
            
            <input type="text" id="simple-input" placeholder="在这里输入文本">
            <div id="input-result" class="result">等待文本输入...</div>
            
            <script>
                document.getElementById('simple-btn').onclick = function() {
                    document.getElementById('btn-result').textContent = '按钮被点击了！时间: ' + new Date().toLocaleTimeString();
                    console.log('按钮点击事件触发');
                };
                
                document.getElementById('simple-input').oninput = function(e) {
                    document.getElementById('input-result').textContent = '输入内容: ' + e.target.value;
                    console.log('输入事件触发:', e.target.value);
                };
            </script>
        </body>
        </html>
        """
        
        try:
            self.log.notice("设置测试页面...")
            page.set_content(html)
            time.sleep(2)
            
            self.log.notice("=== 测试1: 检查GhostCursor是否可用 ===")
            if ghost:
                self.log.success("GhostCursor 已初始化")
            else:
                self.log.err("GhostCursor 未初始化")
                return
            
            self.log.notice("=== 测试2: 点击简单按钮 ===")
            success = ghost.safe_click_element('#simple-btn')
            self.log.notice(f"按钮点击结果: {success}")
            time.sleep(2)
            
            self.log.notice("=== 测试3: 点击输入框并输入文本 ===")
            success = ghost.safe_click_element('#simple-input')
            self.log.notice(f"输入框点击结果: {success}")
            
            if success:
                time.sleep(1)
                ghost.human_type("Hello World!", typing_speed=0.1)
                self.log.notice("文本输入完成")
            
            time.sleep(2)
            
            self.log.notice("=== 测试4: 再次点击按钮 ===")
            success = ghost.safe_click_element('#simple-btn')
            self.log.notice(f"第二次按钮点击结果: {success}")
            time.sleep(2)
            
            self.log.success("所有基本测试完成！")
            
        except Exception as e:
            self.log.err(f"测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
    
    def test_coordinate_click(self):
        """测试坐标点击"""
        page = self.page
        ghost = self.ghost_cursor
        
        try:
            self.log.notice("=== 测试5: 坐标点击 ===")
            
            # 获取页面尺寸
            viewport = page.viewport_size
            if viewport:
                center_x = viewport['width'] // 2
                center_y = viewport['height'] // 2
                
                self.log.notice(f"页面中心坐标: ({center_x}, {center_y})")
                
                # 移动到中心
                ghost.move_to(center_x, center_y, duration=1.0)
                time.sleep(1)
                
                # 点击中心
                ghost.click()
                self.log.notice("中心点击完成")
                time.sleep(1)
                
                # 移动到其他位置
                ghost.move_to(center_x + 100, center_y + 50, duration=0.8)
                time.sleep(1)
                
                self.log.success("坐标点击测试完成")
            
        except Exception as e:
            self.log.err(f"坐标点击测试出错: {e}")
            import traceback
            traceback.print_exc()
    
    def worker(self):
        """主要工作函数"""
        try:
            self.log.notice("开始简单GhostCursor测试...")
            
            # 基本功能测试
            self.test_basic_functionality()
            
            # 坐标点击测试
            self.test_coordinate_click()
            
            self.log.success("所有测试完成！")
            
            # 保持浏览器打开一段时间以便观察结果
            self.log.notice("保持浏览器打开5秒以便观察结果...")
            time.sleep(5)
            
        except Exception as e:
            self.log.err(f"测试过程中出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=== 简单 GhostCursor 测试 ===")
    print("测试修复后的基本点击和输入功能")
    print("=" * 40)
    
    try:
        with SimpleGhostTest() as test:
            if test.new_chrome_browser():
                test.worker()
            else:
                print("浏览器启动失败")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
