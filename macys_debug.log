[2025/07/30 12:10:57] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:10:57] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:10:57] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:11:05] [MainThread] [Notice] === 页面加载完成，开始调试 ===
[2025/07/30 12:11:05] [MainThread] [Notice] === 查找所有按钮元素 ===
[2025/07/30 12:11:05] [MainThread] [Notice] 找到 48 个按钮
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 1: 文本=' Skip to main content ', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 2: 文本='Shipping To', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 3: 文本='Shop All', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 4: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 5: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 6: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 7: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 8: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 9: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 按钮 10: 文本='', 可见=True
[2025/07/30 12:11:05] [MainThread] [Notice] === 查找包含'25%'的元素 ===
[2025/07/30 12:11:05] [MainThread] [Notice] 找到 39 个包含'25%'的元素
[2025/07/30 12:11:05] [MainThread] [Debug] 25%元素 1: 标签=HTML, 文本='@keyframes atNodeInserted3656 {from {opacity:0.99}...', 可见=True
[2025/07/30 12:11:05] [MainThread] [Debug] 25%元素 2: 标签=BODY, 文本='
    
    
    
    
      
    
    var utag_data...', 可见=True
[2025/07/30 12:11:07] [MainThread] [Debug] 25%元素 3: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:11:07] [MainThread] [Debug] 25%元素 4: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:11:07] [MainThread] [Debug] 25%元素 5: 标签=HEADER, 文本='We now ship to 200 locations worldwide DetailsOrde...', 可见=True
[2025/07/30 12:11:07] [MainThread] [Notice] === 查找包含'Claim'的元素 ===
[2025/07/30 12:11:07] [MainThread] [Notice] 找到 0 个包含'Claim'的元素
[2025/07/30 12:11:07] [MainThread] [Notice] === 查找包含'Off'的按钮 ===
[2025/07/30 12:11:07] [MainThread] [Notice] 找到 0 个包含'Off'的按钮
[2025/07/30 12:11:07] [MainThread] [Notice] === 测试不同的选择器 ===
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button:has-text("Click to Claim 25% Off")': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button:has-text("25% Off")': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button:has-text("Claim")': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button:has-text("Get")': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '[data-testid*="claim"]': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '[class*="claim"]': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '[id*="claim"]': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button[class*="offer"]': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 'button[class*="coupon"]': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '.popup button': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '.modal button': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Debug] 选择器 '.overlay button': 找到 0 个元素
[2025/07/30 12:11:07] [MainThread] [Notice] === 保存页面截图 ===
[2025/07/30 12:11:08] [MainThread] [Notice] 截图已保存为 macys_debug_screenshot.png
[2025/07/30 12:11:08] [MainThread] [Notice] === 保存页面HTML ===
[2025/07/30 12:11:08] [MainThread] [Notice] 页面HTML已保存为 macys_debug_page.html
[2025/07/30 12:11:08] [MainThread] [Notice] === 测试GhostCursor在实际页面上的表现 ===
[2025/07/30 12:11:08] [MainThread] [Notice] 移动到页面中心: (640, 360)
[2025/07/30 12:11:43] [MainThread] [Debug] 鼠标移动到: (640, 360)
[2025/07/30 12:11:44] [MainThread] [Notice] 执行随机移动
[2025/07/30 12:12:00] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:17:05] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:17:05] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:17:05] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:17:05] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:17:10] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:17:15] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:17:20] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:17:25] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:17:30] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:17:35] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:17:40] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:17:40] [MainThread] [Error] ❌ 等待 30 秒后仍未检测到弹窗
[2025/07/30 12:17:40] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:17:40] [MainThread] [Notice] 开始监听网络请求...
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/inbox-v2_80c5f44531fa694e643c58392143dd81.br.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/sms-v2_e39203556bab2366e56296ce42e974a7.br.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_c05f8c5551fa6b964660ad61916291c1.br.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/jquery-3.7.1.min.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/website-76167ee381b852a4667f2b6fd2cf9f63.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaign-index-live-4713aa8bb736d66660e05bbcbef3adf3.js
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/local_storage_frame17.min.html
[2025/07/30 12:17:40] [MainThread] [Debug] 📡 BounceX请求: GET https://ib.adnxs.com/bounce?%2Fsetuid%3Fentity%3D315%26code%3DJFQdLa7qRFz3y3LyCfZjRGR9SwOsNs-OUzRGGMsMIAQ%26consent%3D1
[2025/07/30 12:17:42] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:17:42] [MainThread] [Notice] === 等待弹窗出现信号 ===
[2025/07/30 12:17:47] [MainThread] [Debug] 已等待 5 秒，继续监听...
[2025/07/30 12:17:52] [MainThread] [Debug] 已等待 10 秒，继续监听...
[2025/07/30 12:17:57] [MainThread] [Debug] 已等待 15 秒，继续监听...
[2025/07/30 12:18:02] [MainThread] [Debug] 已等待 20 秒，继续监听...
[2025/07/30 12:18:07] [MainThread] [Debug] 已等待 25 秒，继续监听...
[2025/07/30 12:18:12] [MainThread] [Debug] 已等待 30 秒，继续监听...
[2025/07/30 12:18:12] [MainThread] [Error] ❌ 等待 30 秒后仍未检测到弹窗信号
[2025/07/30 12:18:12] [MainThread] [Notice] === 页面调试开始 ===
[2025/07/30 12:18:12] [MainThread] [Notice] === 查找所有按钮元素 ===
[2025/07/30 12:18:12] [MainThread] [Notice] 找到 48 个按钮
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 1: 文本=' Skip to main content ', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 2: 文本='Shipping To', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 3: 文本='Shop All', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 4: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 5: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 6: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 7: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 8: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 9: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Debug] 按钮 10: 文本='', 可见=True
[2025/07/30 12:18:12] [MainThread] [Notice] === 查找包含'25%'的元素 ===
[2025/07/30 12:18:12] [MainThread] [Notice] 找到 39 个包含'25%'的元素
[2025/07/30 12:18:13] [MainThread] [Debug] 25%元素 1: 标签=HTML, 文本='@keyframes atNodeInserted3752 {from {opacity:0.99}...', 可见=True
[2025/07/30 12:18:13] [MainThread] [Debug] 25%元素 2: 标签=BODY, 文本='
    
    
    
    
      
    
    var utag_data...', 可见=True
[2025/07/30 12:18:13] [MainThread] [Debug] 25%元素 3: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:18:13] [MainThread] [Debug] 25%元素 4: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:18:13] [MainThread] [Debug] 25%元素 5: 标签=HEADER, 文本='We now ship to 200 locations worldwide DetailsOrde...', 可见=True
[2025/07/30 12:18:13] [MainThread] [Notice] === 查找包含'Claim'的元素 ===
[2025/07/30 12:18:13] [MainThread] [Notice] 找到 0 个包含'Claim'的元素
[2025/07/30 12:18:13] [MainThread] [Notice] === 查找包含'Off'的按钮 ===
[2025/07/30 12:18:13] [MainThread] [Notice] 找到 0 个包含'Off'的按钮
[2025/07/30 12:18:13] [MainThread] [Notice] === 测试不同的选择器 ===
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button:has-text("Click to Claim 25% Off")': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button:has-text("25% Off")': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button:has-text("Claim")': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button:has-text("Get")': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '[data-testid*="claim"]': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '[class*="claim"]': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '[id*="claim"]': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button[class*="offer"]': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 'button[class*="coupon"]': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '.popup button': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '.modal button': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '.overlay button': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '[role="dialog"] button': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '.be-ix-link-block': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Debug] 选择器 '.be-ix-button': 找到 0 个元素
[2025/07/30 12:18:13] [MainThread] [Notice] === 保存页面截图 ===
[2025/07/30 12:18:14] [MainThread] [Notice] 截图已保存为 macys_debug_screenshot_no_popup.png
[2025/07/30 12:18:14] [MainThread] [Notice] === 保存页面HTML ===
[2025/07/30 12:18:14] [MainThread] [Notice] 页面HTML已保存为 macys_debug_page_no_popup.html
[2025/07/30 12:18:14] [MainThread] [Notice] === 测试GhostCursor在实际页面上的表现 ===
[2025/07/30 12:18:14] [MainThread] [Notice] 移动到页面中心: (640, 360)
[2025/07/30 12:18:25] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:19:01] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:19:01] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:19:01] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:19:01] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:19:05] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:19:10] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:19:15] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:19:20] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:19:25] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:19:30] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:19:35] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:19:35] [MainThread] [Error] ❌ 等待 30 秒后仍未检测到弹窗
[2025/07/30 12:19:35] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:19:35] [MainThread] [Notice] 开始监听网络请求...
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/inbox-v2_80c5f44531fa694e643c58392143dd81.br.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/sms-v2_e39203556bab2366e56296ce42e974a7.br.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_c05f8c5551fa6b964660ad61916291c1.br.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/jquery-3.7.1.min.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/website-76167ee381b852a4667f2b6fd2cf9f63.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaign-index-live-4713aa8bb736d66660e05bbcbef3adf3.js
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/local_storage_frame17.min.html
[2025/07/30 12:19:35] [MainThread] [Debug] 📡 BounceX请求: GET https://ib.adnxs.com/bounce?%2Fsetuid%3Fentity%3D315%26code%3DJl2m9lX8aBQk7eoLw2B4Uxl4dsQyCC_26J8ftoJbbog%26consent%3D1
[2025/07/30 12:19:38] [MainThread] [Notice] === 等待弹窗出现信号 ===
[2025/07/30 12:19:43] [MainThread] [Debug] 已等待 5 秒，继续监听...
[2025/07/30 12:19:48] [MainThread] [Debug] 已等待 10 秒，继续监听...
[2025/07/30 12:19:50] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:19:50] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:19:50] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:19:50] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:19:50] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:19:50] [MainThread] [Notice] cleanup.
[2025/07/30 12:19:50] [MainThread] [Notice] exit.
[2025/07/30 12:20:14] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:20:14] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:20:14] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:20:14] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:20:18] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:20:23] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:20:28] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:20:33] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:20:38] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:20:43] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:20:48] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:20:53] [MainThread] [Debug] 已等待 35 秒...
[2025/07/30 12:20:58] [MainThread] [Debug] 已等待 40 秒...
[2025/07/30 12:21:03] [MainThread] [Debug] 已等待 45 秒...
[2025/07/30 12:21:08] [MainThread] [Debug] 已等待 50 秒...
[2025/07/30 12:21:13] [MainThread] [Debug] 已等待 55 秒...
[2025/07/30 12:21:14] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:21:14] [MainThread] [Notice] cleanup.
[2025/07/30 12:21:14] [MainThread] [Notice] exit.
[2025/07/30 12:23:51] [MainThread] [Success] 获取邮箱: {"age":36,"birthday":{"day":9,"mon":11,"year":1989},"email":"<EMAIL>","first_name":"Jeffery","last_name":"Bell","password":"Abellt3bgh01989","phone_number":"6023798042"}
[2025/07/30 12:23:51] [MainThread] [Notice] 获取代理...
[2025/07/30 12:23:51] [MainThread] [Success] 代理检测通过: 127.0.0.1:50288, 0.179445s
[2025/07/30 12:23:51] [MainThread] [Notice] 使用代理: 127.0.0.1:50288
[2025/07/30 12:23:51] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:23:51] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:23:51] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:23:51] [MainThread] [Notice] 当前使用代理: 127.0.0.1:50288
[2025/07/30 12:23:51] [MainThread] [Notice] 当前使用邮箱: {"age":36,"birthday":{"day":9,"mon":11,"year":1989},"email":"<EMAIL>","first_name":"Jeffery","last_name":"Bell","password":"Abellt3bgh01989","phone_number":"6023798042"}
[2025/07/30 12:23:51] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:23:59] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:24:04] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:24:09] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:24:14] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:24:19] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:24:24] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:24:29] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:24:34] [MainThread] [Debug] 已等待 35 秒...
[2025/07/30 12:24:39] [MainThread] [Debug] 已等待 40 秒...
[2025/07/30 12:24:45] [MainThread] [Debug] 已等待 45 秒...
[2025/07/30 12:24:50] [MainThread] [Debug] 已等待 50 秒...
[2025/07/30 12:24:55] [MainThread] [Debug] 已等待 55 秒...
[2025/07/30 12:25:00] [MainThread] [Debug] 已等待 60 秒...
[2025/07/30 12:25:00] [MainThread] [Error] ❌ 等待 60 秒后仍未检测到弹窗
[2025/07/30 12:25:00] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/macys-deals-and-coupons?id=334356
[2025/07/30 12:25:00] [MainThread] [Notice] 使用代理: 127.0.0.1:50288
[2025/07/30 12:25:00] [MainThread] [Notice] 使用邮箱: {"age":36,"birthday":{"day":9,"mon":11,"year":1989},"email":"<EMAIL>","first_name":"Jeffery","last_name":"Bell","password":"Abellt3bgh01989","phone_number":"6023798042"}
[2025/07/30 12:25:00] [MainThread] [Notice] 开始监听网络请求...
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/inbox-v2_80c5f44531fa694e643c58392143dd81.br.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/sms-v2_e39203556bab2366e56296ce42e974a7.br.js
[2025/07/30 12:25:00] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_c05f8c5551fa6b964660ad61916291c1.br.js
[2025/07/30 12:25:04] [MainThread] [Notice] === 等待弹窗出现信号 ===
[2025/07/30 12:25:09] [MainThread] [Debug] 已等待 5 秒，继续监听...
[2025/07/30 12:25:14] [MainThread] [Debug] 已等待 10 秒，继续监听...
[2025/07/30 12:25:19] [MainThread] [Debug] 已等待 15 秒，继续监听...
[2025/07/30 12:25:24] [MainThread] [Debug] 已等待 20 秒，继续监听...
[2025/07/30 12:25:29] [MainThread] [Debug] 已等待 25 秒，继续监听...
[2025/07/30 12:25:34] [MainThread] [Debug] 已等待 30 秒，继续监听...
[2025/07/30 12:25:34] [MainThread] [Error] ❌ 等待 30 秒后仍未检测到弹窗信号
[2025/07/30 12:25:34] [MainThread] [Notice] === 页面调试开始 ===
[2025/07/30 12:25:34] [MainThread] [Notice] === 查找所有按钮元素 ===
[2025/07/30 12:25:34] [MainThread] [Notice] 找到 48 个按钮
[2025/07/30 12:25:34] [MainThread] [Debug] 按钮 1: 文本=' Skip to main content ', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 2: 文本='Shipping To', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 3: 文本='Shop All', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 4: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 5: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 6: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 7: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 8: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 9: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 按钮 10: 文本='', 可见=True
[2025/07/30 12:25:35] [MainThread] [Notice] === 查找包含'25%'的元素 ===
[2025/07/30 12:25:35] [MainThread] [Notice] 找到 39 个包含'25%'的元素
[2025/07/30 12:25:35] [MainThread] [Debug] 25%元素 1: 标签=HTML, 文本='@keyframes atNodeInserted4719 {from {opacity:0.99}...', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 25%元素 2: 标签=BODY, 文本='
    
    
    
    
      
    
    var utag_data...', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 25%元素 3: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 25%元素 4: 标签=DIV, 文本=' Skip to main content We now ship to 200 locations...', 可见=True
[2025/07/30 12:25:35] [MainThread] [Debug] 25%元素 5: 标签=HEADER, 文本='We now ship to 200 locations worldwide DetailsOrde...', 可见=True
[2025/07/30 12:25:35] [MainThread] [Notice] === 查找包含'Claim'的元素 ===
[2025/07/30 12:25:35] [MainThread] [Notice] 找到 0 个包含'Claim'的元素
[2025/07/30 12:25:35] [MainThread] [Notice] === 查找包含'Off'的按钮 ===
[2025/07/30 12:25:35] [MainThread] [Notice] 找到 0 个包含'Off'的按钮
[2025/07/30 12:25:35] [MainThread] [Notice] === 测试不同的选择器 ===
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button:has-text("Click to Claim 25% Off")': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button:has-text("25% Off")': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button:has-text("Claim")': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button:has-text("Get")': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '[data-testid*="claim"]': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '[class*="claim"]': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '[id*="claim"]': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button[class*="offer"]': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 'button[class*="coupon"]': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '.popup button': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '.modal button': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '.overlay button': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '[role="dialog"] button': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '.be-ix-link-block': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Debug] 选择器 '.be-ix-button': 找到 0 个元素
[2025/07/30 12:25:35] [MainThread] [Notice] === 保存页面截图 ===
[2025/07/30 12:25:36] [MainThread] [Notice] 截图已保存为 macys_debug_screenshot_no_popup.png
[2025/07/30 12:25:36] [MainThread] [Notice] === 保存页面HTML ===
[2025/07/30 12:25:36] [MainThread] [Notice] 页面HTML已保存为 macys_debug_page_no_popup.html
[2025/07/30 12:25:36] [MainThread] [Notice] === 测试GhostCursor在实际页面上的表现 ===
[2025/07/30 12:25:36] [MainThread] [Notice] 移动到页面中心: (640, 360)
[2025/07/30 12:25:43] [MainThread] [Error] 鼠标移动失败: Mouse.move: Target page, context or browser has been closed
[2025/07/30 12:25:44] [MainThread] [Notice] 执行随机移动
[2025/07/30 12:25:44] [MainThread] [Error] 鼠标移动失败: Mouse.move: Target page, context or browser has been closed
[2025/07/30 12:25:44] [MainThread] [Error] 鼠标移动失败: Mouse.move: Target page, context or browser has been closed
[2025/07/30 12:25:44] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:25:44] [MainThread] [Error] [PlayWrightFrameWork.cleanup] BrowserContext.close: Target page, context or browser has been closed
Browser logs:

<launching> C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --proxy-server=http://127.0.0.1:50288 --proxy-bypass-list=<-loopback> --disable-blink-features=AutomationControlled --user-data-dir=C:\Users\<USER>\AppData\Local\Temp\playwright_chromiumdev_profile-8QvZEK --remote-debugging-pipe --no-startup-window
<launched> pid=11016
[pid=11016][err] [11016:9976:0730/122405.673:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[pid=11016][err] [11016:9976:0730/122416.722:ERROR:google_apis\gcm\engine\connection_factory_impl.cc:483] ConnectionHandler failed with net error: -2
[pid=11016][err] [11016:9976:0730/122426.990:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[pid=11016][err] [11016:3172:0730/122427.390:ERROR:CONSOLE:1] "Failed getting cookie attribute: $x-enc", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122430.358:ERROR:CONSOLE:1] "Failed getting cookie attribute: $x-enc", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122435.352:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122435.352:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122435.352:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122435.352:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122445.385:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122445.385:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122445.385:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122451.529:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122451.529:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122451.529:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122451.529:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122500.268:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122500.268:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122500.272:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122500.272:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122503.414:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122503.414:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122503.414:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122503.414:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:9976:0730/122517.234:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[pid=11016][err] [11016:3172:0730/122539.277:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122539.277:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122539.281:ERROR:CONSOLE:1] "Failed getting cookie attribute: Version", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122539.281:ERROR:CONSOLE:1] "Failed getting cookie attribute: Comment", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016][err] [11016:3172:0730/122540.947:ERROR:CONSOLE:1] "Failed getting cookie attribute: $x-enc", source: devtools://devtools/bundled/core/sdk/sdk.js (1)
[pid=11016] <gracefully close start>
[2025/07/30 12:25:44] [MainThread] [Notice] cleanup.
[2025/07/30 12:25:44] [MainThread] [Notice] exit.
[2025/07/30 12:28:36] [MainThread] [Success] 获取邮箱: {"age":76,"birthday":{"day":20,"mon":5,"year":1949},"email":"<EMAIL>","first_name":"Christopher","last_name":"Ramos","password":"Aramosma2a771949","phone_number":"6022693987"}
[2025/07/30 12:28:36] [MainThread] [Notice] 获取代理...
[2025/07/30 12:28:37] [MainThread] [Success] 代理检测通过: 127.0.0.1:50010, 0.549086s
[2025/07/30 12:28:37] [MainThread] [Notice] 使用代理: 127.0.0.1:50010
[2025/07/30 12:28:37] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:28:37] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:28:37] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:28:37] [MainThread] [Notice] 当前使用代理: 127.0.0.1:50010
[2025/07/30 12:28:37] [MainThread] [Notice] 当前使用邮箱: {"age":76,"birthday":{"day":20,"mon":5,"year":1949},"email":"<EMAIL>","first_name":"Christopher","last_name":"Ramos","password":"Aramosma2a771949","phone_number":"6022693987"}
[2025/07/30 12:28:37] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 12:28:41] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:28:46] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:28:51] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:28:56] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:29:01] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:29:06] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:29:11] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:29:16] [MainThread] [Debug] 已等待 35 秒...
[2025/07/30 12:29:21] [MainThread] [Debug] 已等待 40 秒...
[2025/07/30 12:29:26] [MainThread] [Debug] 已等待 45 秒...
[2025/07/30 12:29:31] [MainThread] [Debug] 已等待 50 秒...
[2025/07/30 12:29:36] [MainThread] [Debug] 已等待 55 秒...
[2025/07/30 12:29:42] [MainThread] [Debug] 已等待 60 秒...
[2025/07/30 12:29:42] [MainThread] [Error] ❌ 等待 60 秒后仍未检测到弹窗
[2025/07/30 12:29:42] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 12:29:42] [MainThread] [Notice] 使用代理: 127.0.0.1:50010
[2025/07/30 12:29:42] [MainThread] [Notice] 使用邮箱: {"age":76,"birthday":{"day":20,"mon":5,"year":1949},"email":"<EMAIL>","first_name":"Christopher","last_name":"Ramos","password":"Aramosma2a771949","phone_number":"6022693987"}
[2025/07/30 12:29:42] [MainThread] [Notice] 开始监听网络请求...
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/inbox-v2_80c5f44531fa694e643c58392143dd81.br.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/sms-v2_e39203556bab2366e56296ce42e974a7.br.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_c05f8c5551fa6b964660ad61916291c1.br.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/jquery-3.7.1.min.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/website-76167ee381b852a4667f2b6fd2cf9f63.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaign-index-live-4713aa8bb736d66660e05bbcbef3adf3.js
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/local_storage_frame17.min.html
[2025/07/30 12:29:42] [MainThread] [Debug] 📡 BounceX请求: GET https://ib.adnxs.com/bounce?%2Fsetuid%3Fentity%3D315%26code%3D5BOVa4T9jBFzq5P1azbwB5r65FscztiPB_dxfWFLRs8%26consent%3D1
[2025/07/30 12:29:43] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaigns/2842942-04fd4387ec4b4b73c0002fa171a01763.js
[2025/07/30 12:29:45] [MainThread] [Notice] === 等待弹窗出现信号 ===
[2025/07/30 12:29:50] [MainThread] [Debug] 已等待 5 秒，继续监听...
[2025/07/30 12:29:55] [MainThread] [Debug] 已等待 10 秒，继续监听...
[2025/07/30 12:29:58] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:29:58] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:30:15] [MainThread] [Success] 获取邮箱: {"age":49,"birthday":{"day":17,"mon":1,"year":1976},"email":"<EMAIL>","first_name":"Daniel","last_name":"Gordon","password":"Agordonatvvlb1976","phone_number":"5036327488"}
[2025/07/30 12:30:15] [MainThread] [Notice] 获取代理...
[2025/07/30 12:30:15] [MainThread] [Success] 代理检测通过: 127.0.0.1:50086, 0.468119s
[2025/07/30 12:30:15] [MainThread] [Notice] 使用代理: 127.0.0.1:50086
[2025/07/30 12:30:16] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:30:16] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:30:16] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:30:16] [MainThread] [Notice] 当前使用代理: 127.0.0.1:50086
[2025/07/30 12:30:16] [MainThread] [Notice] 当前使用邮箱: {"age":49,"birthday":{"day":17,"mon":1,"year":1976},"email":"<EMAIL>","first_name":"Daniel","last_name":"Gordon","password":"Agordonatvvlb1976","phone_number":"5036327488"}
[2025/07/30 12:30:16] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 12:30:20] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:30:25] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:30:30] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:30:35] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:30:40] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:30:45] [MainThread] [Debug] 已等待 25 秒...
[2025/07/30 12:30:50] [MainThread] [Debug] 已等待 30 秒...
[2025/07/30 12:30:55] [MainThread] [Debug] 已等待 35 秒...
[2025/07/30 12:31:00] [MainThread] [Debug] 已等待 40 秒...
[2025/07/30 12:31:05] [MainThread] [Debug] 已等待 45 秒...
[2025/07/30 12:31:11] [MainThread] [Debug] 已等待 50 秒...
[2025/07/30 12:31:16] [MainThread] [Debug] 已等待 55 秒...
[2025/07/30 12:31:21] [MainThread] [Debug] 已等待 60 秒...
[2025/07/30 12:31:21] [MainThread] [Error] ❌ 等待 60 秒后仍未检测到弹窗
[2025/07/30 12:31:21] [MainThread] [Notice] 访问测试URL: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 12:31:21] [MainThread] [Notice] 使用代理: 127.0.0.1:50086
[2025/07/30 12:31:21] [MainThread] [Notice] 使用邮箱: {"age":49,"birthday":{"day":17,"mon":1,"year":1976},"email":"<EMAIL>","first_name":"Daniel","last_name":"Gordon","password":"Agordonatvvlb1976","phone_number":"5036327488"}
[2025/07/30 12:31:21] [MainThread] [Notice] 开始监听网络请求...
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/runtime_c81e76ee00d795b1eebf8d27949f8dc5.br.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/main-v2_548d1f1640c71346f85ffbde4283f56f.br.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/cjs_min_c84323e2726f3e99b307ab7740c6434b.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/inbox-v2_80c5f44531fa694e643c58392143dd81.br.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/sms-v2_e39203556bab2366e56296ce42e974a7.br.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/smart-tag/versioned/onsite-v2_c05f8c5551fa6b964660ad61916291c1.br.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/jquery-3.7.1.min.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/website-76167ee381b852a4667f2b6fd2cf9f63.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaign-index-live-4713aa8bb736d66660e05bbcbef3adf3.js
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/assets/bounce/local_storage_frame17.min.html
[2025/07/30 12:31:21] [MainThread] [Debug] 📡 BounceX请求: GET https://ib.adnxs.com/bounce?%2Fsetuid%3Fentity%3D315%26code%3DSwVAbz03kVAwImH_bpIjpM0eA53ebwKbe4o1n2DuM6k%26consent%3D1
[2025/07/30 12:31:22] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaigns/2842942-04fd4387ec4b4b73c0002fa171a01763.js
[2025/07/30 12:31:22] [MainThread] [Debug] 📡 BounceX请求: GET https://assets.bounceexchange.com/cache/4994/campaigns/2846223-a71b4c9ed8b165f3183f2c05040d5f5d.js
[2025/07/30 12:31:24] [MainThread] [Notice] === 等待弹窗出现信号 ===
[2025/07/30 12:31:29] [MainThread] [Debug] 已等待 5 秒，继续监听...
[2025/07/30 12:31:34] [MainThread] [Debug] 已等待 10 秒，继续监听...
[2025/07/30 12:31:39] [MainThread] [Debug] 已等待 15 秒，继续监听...
[2025/07/30 12:31:44] [MainThread] [Debug] 已等待 20 秒，继续监听...
[2025/07/30 12:31:49] [MainThread] [Debug] 已等待 25 秒，继续监听...
[2025/07/30 12:31:54] [MainThread] [Debug] 已等待 30 秒，继续监听...
[2025/07/30 12:31:59] [MainThread] [Debug] 已等待 35 秒，继续监听...
[2025/07/30 12:32:04] [MainThread] [Debug] 已等待 40 秒，继续监听...
[2025/07/30 12:32:09] [MainThread] [Debug] 已等待 45 秒，继续监听...
[2025/07/30 12:32:14] [MainThread] [Debug] 已等待 50 秒，继续监听...
[2025/07/30 12:32:19] [MainThread] [Debug] 已等待 55 秒，继续监听...
[2025/07/30 12:32:24] [MainThread] [Debug] 已等待 60 秒，继续监听...
[2025/07/30 12:32:29] [MainThread] [Debug] 已等待 65 秒，继续监听...
[2025/07/30 12:32:34] [MainThread] [Debug] 已等待 70 秒，继续监听...
[2025/07/30 12:32:39] [MainThread] [Debug] 已等待 75 秒，继续监听...
[2025/07/30 12:32:41] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:32:41] [MainThread] [Debug] 📡 BounceX请求: GET https://tag.bounceexchange.com/4994/i.js
[2025/07/30 12:32:41] [MainThread] [Notice] cleanup.
[2025/07/30 12:32:41] [MainThread] [Notice] exit.
[2025/07/30 12:35:28] [MainThread] [Success] 获取邮箱: {"age":78,"birthday":{"day":19,"mon":9,"year":1947},"email":"<EMAIL>","first_name":"Joshua","last_name":"Miller","password":"Amillerjnx5mj1947","phone_number":"5202074600"}
[2025/07/30 12:35:28] [MainThread] [Notice] 获取代理...
[2025/07/30 12:35:28] [MainThread] [Success] 代理检测通过: 127.0.0.1:50100, 0.3018s
[2025/07/30 12:35:28] [MainThread] [Notice] 使用代理: 127.0.0.1:50100
[2025/07/30 12:35:28] [MainThread] [Success] new chrome browser success, global_timeout = 15000
[2025/07/30 12:35:28] [MainThread] [Notice] 开始Macy's页面调试...
[2025/07/30 12:35:28] [MainThread] [Notice] === 测试弹窗检测和点击功能 ===
[2025/07/30 12:35:28] [MainThread] [Notice] 当前使用代理: 127.0.0.1:50100
[2025/07/30 12:35:28] [MainThread] [Notice] 当前使用邮箱: {"age":78,"birthday":{"day":19,"mon":9,"year":1947},"email":"<EMAIL>","first_name":"Joshua","last_name":"Miller","password":"Amillerjnx5mj1947","phone_number":"5202074600"}
[2025/07/30 12:35:28] [MainThread] [Notice] 已启用资源拦截: 图片, 字体, 样式表, 媒体文件
[2025/07/30 12:35:28] [MainThread] [Notice] 访问测试URL进行弹窗检测: https://www.macys.com/shop/sale/sale-10-under?id=305358
[2025/07/30 12:35:28] [MainThread] [Debug] ✅ 允许 document: https://www.macys.com/shop/sale/sale-10-under?id=305358...
[2025/07/30 12:35:30] [MainThread] [Debug] 🚫 拦截 font: https://assets.macysassets.com/common/assets/fonts/macys-sans/MacysSans.woff2...
[2025/07/30 12:35:30] [MainThread] [Debug] 🚫 拦截 font: https://assets.macysassets.com/common/assets/fonts/macys-sans/MacysSansMd.woff2...
[2025/07/30 12:35:30] [MainThread] [Debug] 🚫 拦截 font: https://assets.macysassets.com/common/assets/fonts/macys-sans/MacysSansBd.woff2...
[2025/07/30 12:35:30] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.vendor.co...
[2025/07/30 12:35:30] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.app.85919...
[2025/07/30 12:35:30] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.core.vend...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.polyfills...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.runtime.c...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 stylesheet: https://assets.macysassets.com/app/discovery-ui/static/css/app.99884d3057eee55ff...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 stylesheet: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/logo.svg...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://tags.tiqcdn.com/utag/macys/main/prod/utag.sync.js...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://www.macys.com/akam/13/33c2b7aa...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://securepubads.g.doubleclick.net/tag/js/gpt.js...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://s.go-mpulse.net/boomerang/WVZ92-598Q4-C592Z-HWSHG-PBBKW...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/6/optimized/31703446_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/0/optimized/29128700_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/0/optimized/28836720_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/5/optimized/29140135_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/6/optimized/31703446_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/0/optimized/29128700_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/0/optimized/28836720_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://slimages.macysassets.com/is/image/MCY/products/5/optimized/29140135_fpx....
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://www.macys.com/S85Rer8OWG/dD/rH_0tZxK/kE1zGfNa1JfrNG9E/SixucGdeLAQ/Sl/ptY...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://cdn.cookielaw.org/scripttemplates/otSDKStub.js...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://cdn1.adoberesources.net/alloy/2.24.0/alloy.min.js...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 xhr: https://c.go-mpulse.net/api/config.json?key=WVZ92-598Q4-C592Z-HWSHG-PBBKW&d=www....
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 xhr: https://www.macys.com/xapi/navigate/v1/header-footer/switches...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/logo.svg...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/flags/SG.png...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/toys.svg?qlt=85,...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 xhr: https://www.macys.com/S85Rer8OWG/dD/rH_0tZxK/kE1zGfNa1JfrNG9E/SixucGdeLAQ/Sl/ptY...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/logo.svg...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 image: https://assets.macysassets.com/app/navigation-wgl/static/images/flags/SG.png...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 xhr: https://cdn.cookielaw.org/consent/01ccd164-62fb-406d-a827-692c34dd9eda/01ccd164-...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/m202507280101/pubad...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 stylesheet: https://assets.macysassets.com/app/discovery-ui/static/css/common.discovery-ui.0...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.common.di...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.7870.b7a8...
[2025/07/30 12:35:31] [MainThread] [Debug] 🚫 拦截 stylesheet: https://assets.macysassets.com/app/discovery-ui/static/css/7769.9c72ee01850b21f8...
[2025/07/30 12:35:31] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/discovery-ui/static/js/discovery-ui.7769.a770...
[2025/07/30 12:35:32] [MainThread] [Debug] 🚫 拦截 stylesheet: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:32] [MainThread] [Debug] ✅ 允许 script: https://assets.macysassets.com/app/navigation-wgl/2.305.2/app/navigation-wgl/sta...
[2025/07/30 12:35:32] [MainThread] [Debug] ✅ 允许 other: https://securepubads.g.doubleclick.net/pagead/managed/dict/m202507290101/gpt...
[2025/07/30 12:35:32] [MainThread] [Debug] ✅ 允许 xhr: https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location...
[2025/07/30 12:35:32] [MainThread] [Debug] ✅ 允许 xhr: https://www.macys.com/akam/13/pixel_33c2b7aa...
[2025/07/30 12:35:32] [MainThread] [Success] 页面加载完成，耗时: 3.63 秒
[2025/07/30 12:35:32] [MainThread] [Notice] === 资源请求统计 ===
[2025/07/30 12:35:32] [MainThread] [Notice] 总请求数: 52
[2025/07/30 12:35:32] [MainThread] [Notice] 拦截数量: 22 (42.3%)
[2025/07/30 12:35:32] [MainThread] [Notice] 允许数量: 30 (57.7%)
[2025/07/30 12:35:32] [MainThread] [Debug] 详细统计:
[2025/07/30 12:35:32] [MainThread] [Debug]   image: 总计14, 拦截14, 允许0
[2025/07/30 12:35:32] [MainThread] [Debug]   font: 总计3, 拦截3, 允许0
[2025/07/30 12:35:32] [MainThread] [Debug]   stylesheet: 总计5, 拦截5, 允许0
[2025/07/30 12:35:32] [MainThread] [Debug]   document: 总计1, 拦截0, 允许1
[2025/07/30 12:35:32] [MainThread] [Debug]   script: 总计22, 拦截0, 允许22
[2025/07/30 12:35:32] [MainThread] [Debug]   xhr: 总计6, 拦截0, 允许6
[2025/07/30 12:35:32] [MainThread] [Debug]   other: 总计1, 拦截0, 允许1
[2025/07/30 12:35:32] [MainThread] [Notice] 等待弹窗出现...
[2025/07/30 12:35:37] [MainThread] [Debug] 已等待 5 秒...
[2025/07/30 12:35:42] [MainThread] [Debug] 已等待 10 秒...
[2025/07/30 12:35:47] [MainThread] [Debug] 已等待 15 秒...
[2025/07/30 12:35:52] [MainThread] [Debug] 已等待 20 秒...
[2025/07/30 12:35:55] [MainThread] [Notice] COUNT = 0
[2025/07/30 12:35:55] [MainThread] [Error] [PlayWrightFrameWork.cleanup] 'Page.close: \'Route.continue_: \\\'Route.continue_: "Route.continue_: \\\\\\\'fetch\\\\\\\'"\\\'\''
[2025/07/30 12:35:55] [MainThread] [Error] [PlayWrightFrameWork.cleanup] Browser.close: Connection closed while reading from the driver
[2025/07/30 12:35:55] [MainThread] [Notice] cleanup.
[2025/07/30 12:35:55] [MainThread] [Notice] exit.
