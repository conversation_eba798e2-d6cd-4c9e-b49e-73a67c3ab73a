"""
测试修复后的单次移动功能
验证鼠标不会重复移动到同一位置
"""

from playwright_framework import PlayWrightFrameWork
from tool import Log
import time


class SingleMoveTest(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log("single_move_test.log", debug=True))
        self.move_count = 0
        self.move_positions = []
    
    def ok(self):
        return True
    
    def setup_move_tracking(self):
        """设置鼠标移动追踪"""
        original_move_to = self.ghost_cursor.move_to
        
        def tracked_move_to(x, y, duration=None):
            self.move_count += 1
            self.move_positions.append((x, y, time.time()))
            self.log.notice(f"🖱️ 移动 #{self.move_count}: 到位置 ({x}, {y})")
            return original_move_to(x, y, duration)
        
        self.ghost_cursor.move_to = tracked_move_to
    
    def test_single_click(self):
        """测试单次点击是否只移动一次"""
        page = self.page
        ghost = self.ghost_cursor
        
        # 创建测试页面
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>单次移动测试</title>
            <style>
                body { padding: 50px; font-family: Arial, sans-serif; }
                .target { 
                    width: 200px; 
                    height: 80px; 
                    margin: 30px; 
                    padding: 15px; 
                    background: #007bff; 
                    color: white; 
                    border: none; 
                    cursor: pointer;
                    border-radius: 8px;
                    font-size: 18px;
                    display: block;
                }
                .target:hover { background: #0056b3; }
                .clicked { background: #28a745 !important; }
                .info { margin: 20px; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
            </style>
        </head>
        <body>
            <h1>单次移动测试</h1>
            <div class="info">
                <p>测试目标：验证每次点击只移动一次鼠标</p>
                <p>预期：每个按钮点击只应该有一次"鼠标移动到"的日志</p>
            </div>
            
            <button class="target" id="btn1" onclick="markClicked(this, 1)">测试按钮 1</button>
            <button class="target" id="btn2" onclick="markClicked(this, 2)">测试按钮 2</button>
            <button class="target" id="btn3" onclick="markClicked(this, 3)">测试按钮 3</button>
            
            <div class="info" id="result">等待点击...</div>
            
            <script>
                let clickedButtons = [];
                
                function markClicked(button, num) {
                    button.classList.add('clicked');
                    clickedButtons.push(num);
                    
                    const resultDiv = document.getElementById('result');
                    resultDiv.innerHTML = 
                        '<h4>点击结果</h4>' +
                        '<p>已点击按钮: ' + clickedButtons.join(', ') + '</p>' +
                        '<p>总点击数: ' + clickedButtons.length + '</p>';
                }
            </script>
        </body>
        </html>
        """
        
        try:
            self.log.notice("=== 单次移动测试开始 ===")
            
            # 设置移动追踪
            self.setup_move_tracking()
            
            # 设置测试页面
            page.set_content(html_content)
            time.sleep(1)
            
            # 测试点击每个按钮
            buttons = ['#btn1', '#btn2', '#btn3']
            
            for i, button_id in enumerate(buttons, 1):
                self.log.notice(f"\n--- 测试按钮 {i}: {button_id} ---")
                
                # 重置计数器
                move_count_before = self.move_count
                
                # 点击按钮
                start_time = time.time()
                success = ghost.safe_click_element(button_id)
                end_time = time.time()
                
                # 计算移动次数
                moves_for_this_click = self.move_count - move_count_before
                click_duration = end_time - start_time
                
                self.log.notice(f"按钮 {i} 结果:")
                self.log.notice(f"  点击成功: {success}")
                self.log.notice(f"  移动次数: {moves_for_this_click}")
                self.log.notice(f"  总耗时: {click_duration:.3f}秒")
                
                if moves_for_this_click == 1:
                    self.log.success(f"✅ 按钮 {i} 移动次数正确 (1次)")
                elif moves_for_this_click > 1:
                    self.log.err(f"❌ 按钮 {i} 移动次数过多 ({moves_for_this_click}次)")
                else:
                    self.log.err(f"❌ 按钮 {i} 没有移动")
                
                time.sleep(0.5)  # 短暂等待
            
            # 总结测试结果
            self.log.notice(f"\n=== 测试总结 ===")
            self.log.notice(f"总移动次数: {self.move_count}")
            self.log.notice(f"预期移动次数: {len(buttons)}")
            
            if self.move_count == len(buttons):
                self.log.success("✅ 测试通过：每次点击只移动一次")
            else:
                self.log.err(f"❌ 测试失败：移动次数不匹配")
            
            # 显示移动轨迹
            self.log.notice("\n移动轨迹:")
            for i, (x, y, timestamp) in enumerate(self.move_positions, 1):
                self.log.debug(f"  移动 {i}: ({x}, {y}) 时间: {timestamp:.3f}")
            
            # 保存测试结果
            with open("single_move_test_result.log", "w", encoding="utf-8") as f:
                f.write("=== 单次移动测试结果 ===\n")
                f.write(f"总移动次数: {self.move_count}\n")
                f.write(f"预期移动次数: {len(buttons)}\n")
                f.write(f"测试结果: {'通过' if self.move_count == len(buttons) else '失败'}\n")
                f.write("\n移动轨迹:\n")
                for i, (x, y, timestamp) in enumerate(self.move_positions, 1):
                    f.write(f"移动 {i}: ({x}, {y}) 时间: {timestamp:.3f}\n")
                f.write(f"\n测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 截图
            page.screenshot(path="single_move_test_result.png")
            self.log.notice("测试结果已保存")
            
            return self.move_count == len(buttons)
            
        except Exception as e:
            self.log.err(f"单次移动测试出错: {e}")
            return False
    
    def worker(self):
        """主要工作函数"""
        try:
            self.log.notice("开始单次移动测试...")
            
            success = self.test_single_click()
            
            if success:
                self.log.success("🎉 单次移动测试通过！")
            else:
                self.log.err("❌ 单次移动测试失败！")
            
            # 保持浏览器打开
            self.log.notice("保持浏览器打开10秒...")
            time.sleep(10)
            
        except Exception as e:
            self.log.err(f"测试出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=== 单次移动测试 ===")
    print("验证修复后的鼠标移动逻辑")
    print("每次点击应该只移动一次鼠标")
    print("=" * 40)
    
    try:
        with SingleMoveTest() as test:
            if test.new_chrome_browser():
                test.worker()
            else:
                print("浏览器启动失败")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
