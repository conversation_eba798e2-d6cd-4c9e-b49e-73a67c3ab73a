"""
测试快速鼠标移动功能
"""

from playwright_framework import PlayWrightFrameWork
from tool import Log
import time


class FastCursorTest(PlayWrightFrameWork):
    def __init__(self):
        super().__init__(Log("fast_cursor_test.log", debug=True))
    
    def ok(self):
        return True
    
    def test_fast_movement(self):
        """测试快速鼠标移动"""
        page = self.page
        ghost = self.ghost_cursor
        
        # 创建测试页面
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>快速鼠标移动测试</title>
            <style>
                body { padding: 50px; font-family: Arial, sans-serif; }
                .target { 
                    width: 100px; 
                    height: 50px; 
                    margin: 20px; 
                    padding: 10px; 
                    background: #007bff; 
                    color: white; 
                    border: none; 
                    cursor: pointer;
                    border-radius: 5px;
                    font-size: 16px;
                }
                .target:hover { background: #0056b3; }
                .clicked { background: #28a745 !important; }
                .result { margin: 20px; padding: 10px; background: #f8f9fa; }
            </style>
        </head>
        <body>
            <h1>快速鼠标移动测试</h1>
            <p>测试GhostCursor的快速移动和点击功能</p>
            
            <button class="target" id="btn1" onclick="markClicked(this)">按钮 1</button>
            <button class="target" id="btn2" onclick="markClicked(this)">按钮 2</button>
            <button class="target" id="btn3" onclick="markClicked(this)">按钮 3</button>
            <button class="target" id="btn4" onclick="markClicked(this)">按钮 4</button>
            <button class="target" id="btn5" onclick="markClicked(this)">按钮 5</button>
            
            <div class="result" id="result">等待点击...</div>
            
            <script>
                let clickCount = 0;
                let startTime = Date.now();
                
                function markClicked(button) {
                    button.classList.add('clicked');
                    clickCount++;
                    const elapsed = (Date.now() - startTime) / 1000;
                    document.getElementById('result').textContent = 
                        `已点击 ${clickCount} 个按钮，耗时 ${elapsed.toFixed(2)} 秒`;
                }
            </script>
        </body>
        </html>
        """
        
        try:
            self.log.notice("=== 快速鼠标移动测试 ===")
            page.set_content(html_content)
            time.sleep(1)
            
            # 测试快速连续点击
            buttons = ['#btn1', '#btn2', '#btn3', '#btn4', '#btn5']
            
            self.log.notice("开始快速连续点击测试...")
            start_time = time.time()
            
            for i, button_id in enumerate(buttons, 1):
                self.log.debug(f"点击按钮 {i}: {button_id}")
                
                # 记录单次移动时间
                move_start = time.time()
                success = ghost.safe_click_element(button_id)
                move_time = time.time() - move_start
                
                self.log.debug(f"按钮 {i} 点击{'成功' if success else '失败'}，耗时: {move_time:.3f}秒")
                
                # 短暂等待以便观察
                time.sleep(0.1)
            
            total_time = time.time() - start_time
            self.log.success(f"完成5个按钮点击，总耗时: {total_time:.3f}秒，平均每次: {total_time/5:.3f}秒")
            
            # 截图保存结果
            page.screenshot(path="fast_cursor_test_result.png")
            self.log.notice("已保存测试结果截图")
            
            return total_time
            
        except Exception as e:
            self.log.err(f"快速移动测试出错: {e}")
            return None
    
    def test_movement_precision(self):
        """测试移动精度"""
        page = self.page
        ghost = self.ghost_cursor
        
        try:
            self.log.notice("=== 移动精度测试 ===")
            
            # 测试移动到不同位置
            test_positions = [
                (100, 100),
                (500, 200),
                (300, 400),
                (700, 300),
                (200, 500)
            ]
            
            for i, (x, y) in enumerate(test_positions, 1):
                self.log.debug(f"移动到位置 {i}: ({x}, {y})")
                
                move_start = time.time()
                ghost.move_to(x, y)
                move_time = time.time() - move_start
                
                # 检查当前位置
                actual_x, actual_y = ghost.current_x, ghost.current_y
                self.log.debug(f"目标: ({x}, {y}), 实际: ({actual_x}, {actual_y}), 耗时: {move_time:.3f}秒")
                
                time.sleep(0.1)
            
            self.log.success("移动精度测试完成")
            
        except Exception as e:
            self.log.err(f"移动精度测试出错: {e}")
    
    def worker(self):
        """主要工作函数"""
        try:
            self.log.notice("开始快速鼠标测试...")
            
            # 测试1: 快速移动和点击
            total_time = self.test_fast_movement()
            
            # 测试2: 移动精度
            self.test_movement_precision()
            
            # 保存测试结果
            if total_time:
                with open("fast_cursor_performance.log", "w", encoding="utf-8") as f:
                    f.write("=== 快速鼠标性能测试结果 ===\n")
                    f.write(f"5个按钮连续点击总时间: {total_time:.3f}秒\n")
                    f.write(f"平均每次点击时间: {total_time/5:.3f}秒\n")
                    f.write(f"快速模式: 启用\n")
                    f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                
                self.log.notice("性能测试结果已保存")
            
            self.log.success("快速鼠标测试完成！")
            
            # 保持浏览器打开以便观察
            self.log.notice("保持浏览器打开10秒...")
            time.sleep(10)
            
        except Exception as e:
            self.log.err(f"测试出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("=== 快速鼠标移动测试 ===")
    print("测试优化后的GhostCursor移动速度")
    print("=" * 40)
    
    try:
        with FastCursorTest() as test:
            if test.new_chrome_browser():
                test.worker()
            else:
                print("浏览器启动失败")
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
