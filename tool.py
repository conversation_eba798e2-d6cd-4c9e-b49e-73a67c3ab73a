from io import TextIOWrapper
from typing import List
import colorama
from colorama import Fore, Style
import datetime
from threading import RLock, Event
from queue import Queue
import threading
import requests
from typing import Tuple, Optional


def del_file(filename):
    import os
    try:
        os.remove(filename)
    except:
        ...


def file_to_queue(file) -> Queue:
    try:
        with open(file, 'r') as f:
            l = f.read().splitlines()
            return list_to_queue(l)
    except:
        return Queue()


def file_to_list(file) -> List:
    try:
        with open(file, 'r') as f:
            l = f.read().splitlines()
            return l
    except:
        return []


def str_2_dict(s: str) -> dict:
    l = s.split(';')
    d = {}
    for c in l:
        t = c.split('=')
        d[t[0].lstrip()] = t[1]
    return d


def list_to_queue(l: List):
    _q = Queue()
    for e in l:
        _q.put_nowait(e)
    return _q


def list_to_file(l: List, filename: str):
    '''
    列表中的数据保存到文件中，覆盖
    '''
    try:
        with open(filename, 'w') as f:
            f.writelines([f'{line}\n' for line in l])
    except:
        import traceback
        traceback.print_exc()
        ...


def str_time(format_='%Y/%m/%d %H:%M:%S'):
    try:
        dt_sec = datetime.datetime.now().strftime(format_)
        return dt_sec
    except:
        return str(datetime.datetime.now())


def signal_register(event: Event=None, exit_: bool=True):
    import signal
    import os
    import platform

    def handle_sigint(signum, frame):
        try:
            print('接收到终止信号, 正在停止...')
        except:
            ...
        if event:
            event.set()
        if exit_:
            os._exit(0)

    signal.signal(signal.SIGINT, handle_sigint)
    signal.signal(signal.SIGTERM, handle_sigint)
    if platform.system() != 'Windows':
        signal.signal(signal.SIGUSR1, handle_sigint)


class Log:
    initialized = False
    def __init__(self, file=None, debug=True) -> None:
        '''
        日志系统

        '''
        if not self.initialized:
            colorama.init()
            self.initialize()
            
        self._file: TextIOWrapper = None
        if file is not None:
            self._file = open(file, 'a', encoding='utf-8')
        
        self._debug = debug

        self._lock = RLock()
    
    def __del__(self) -> None:
        if self._file is not None:
            self._file.close()
    
    def _write_file(self, s: str):
        if self._file is None:
            return
        try:
            self._file.write(s + '\n')
            self._file.flush()
        except:
            ...
    
    def set_debug(self, b: bool):
        self._debug = b
    
    @classmethod
    def initialize(cls):
        cls.initialized = True
    
    @staticmethod
    def time_format():
        dt_sec = datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        return dt_sec
    
    def _color_test(self):
        print(Fore.RED + '颜色测试 RED')
        print(Fore.LIGHTRED_EX + '颜色测试 LIGHTRED_EX')
        print(Fore.RED + Style.DIM + '颜色测试 RED DIM')

        print(Fore.BLACK + '颜色测试 BLACK')
        print(Fore.LIGHTBLACK_EX + '颜色测试 LIGHTBLACK_EX')
        print(Fore.BLACK + Style.DIM + '颜色测试BLACK DIM')

        print(Fore.GREEN + '颜色测试 GREEN')
        print(Fore.LIGHTGREEN_EX + '颜色测试 LIGHTGREEN_EX')
        print(Fore.GREEN + Style.DIM + '颜色测试 GREEN DIM')

        print(Fore.YELLOW + '颜色测试 YELLOW')
        print(Fore.LIGHTYELLOW_EX + '颜色测试 LIGHTYELLOW_EX')
        print(Fore.YELLOW + Style.DIM + '颜色测试 YELLOW DIM')

        print(Fore.BLUE + '颜色测试 BLUE')
        print(Fore.LIGHTBLUE_EX + '颜色测试 LIGHTBLUE_EX')
        print(Fore.BLUE + Style.DIM + '颜色测试 BLUE DIM')

        print(Fore.MAGENTA + '颜色测试 MAGENTA')
        print(Fore.LIGHTMAGENTA_EX + '颜色测试 LIGHTMAGENTA_EX')
        print(Fore.MAGENTA + Style.DIM + '颜色测试 MAGENTA DIM')

        print(Fore.CYAN + '颜色测试 CYAN')
        print(Fore.LIGHTCYAN_EX + '颜色测试 LIGHTCYAN_EX')
        print(Fore.CYAN + Style.DIM + '颜色测试 CYAN DIM')

        print(Fore.WHITE + '颜色测试 WHITE')
        print(Fore.LIGHTWHITE_EX + '颜色测试 LIGHTWHITE_EX')
        print(Fore.WHITE + Style.DIM + '颜色测试 WHITE DIM')

        print(Fore.RESET + '颜色测试 RESET' + Style.RESET_ALL)
    
    @staticmethod
    def _red(s: str):
        return Fore.RED + s + Style.RESET_ALL
    
    @staticmethod
    def _cyan(s: str):
        return Fore.CYAN + s + Style.RESET_ALL
    
    @staticmethod
    def _green(s: str):
        return Fore.GREEN + s + Style.RESET_ALL
    
    @staticmethod
    def _yellow(s: str):
        return Fore.YELLOW + s + Style.RESET_ALL
    
    @staticmethod
    def _blue_ex(s: str):
        return Fore.LIGHTBLUE_EX + s + Style.RESET_ALL
    
    @staticmethod
    def _magenta_ex(s: str):
        return Fore.LIGHTMAGENTA_EX + s + Style.RESET_ALL

    @staticmethod
    def _thread_name():
        s = threading.current_thread().name
        return s
    
    def _file_format(self, dt, level: str, msg):
        _th = threading.current_thread().name
        return f'[{dt}] [{_th}] [{level}] {msg}'
    
    @staticmethod
    def _console_format(level: str, msg):
        _th = threading.current_thread().name
        return f'[{_th}] [{level}] {msg}'
    
    def err(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            print(self._cyan(f'[{dt_ms}] ') + self._red(self._console_format('Error', s)))

            # print(self._cyan(f'[{dt_ms}] ') + self._red(f'[Error] {s}'))
            # self._write_file(f'[Error] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Error', s))
    
    def success(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            print(self._cyan(f'[{dt_ms}] ') + self._green(self._console_format('Success', s)))

            # print(self._cyan(f'[{dt_ms}] ') + self._green(f'[Success] {s}'))
            # self._write_file(f'[Success] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Success', s))

    def msg(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            print(self._cyan(f'[{dt_ms}] ') + self._console_format('Message', s))

            # print(self._cyan(f'[{dt_ms}] ') + f'[Message] {s}')
            # self._write_file(f'[Message] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Message', s))

    def debug(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            if self._debug:
                print(self._cyan(f'[{dt_ms}] ') + self._yellow(self._console_format('Debug', s)))

                # print(self._cyan(f'[{dt_ms}] ') + self._yellow(f'[Debug] {s}'))
            # self._write_file(f'[Debug] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Debug', s))
    
    def notice(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            print(self._cyan(f'[{dt_ms}] ') + self._blue_ex(self._console_format('Notice', s)))

            # print(self._cyan(f'[{dt_ms}] ') + self._blue_ex(f'[Notice] {s}'))
            # self._write_file(f'[Notice] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Notice', s))
    
    def warning(self, s: str):
        with self._lock:
            dt_ms = self.time_format()
            print(self._cyan(f'[{dt_ms}] ') + self._magenta_ex(self._console_format('Warning', s)))

            # print(self._cyan(f'[{dt_ms}] ') + self._magenta_ex(f'[Warning] {s}'))
            # self._write_file(f'[Warning] [{dt_ms}] {s}')
            self._write_file(self._file_format(dt_ms, 'Warning', s))


def check_ip(ip_port: str, log_instance: Log=None) -> bool:
    return True

def check_proxy(proxy: str, timeout: int = 5) -> Tuple[bool, Optional[float]]:
    proxies = {
        'https': f'http://{proxy}',
        'http': f'http://{proxy}'
    }

    test_url = "https://www.apple.com/library/test/success.html"

    try:
        response = requests.get(test_url, proxies=proxies, timeout=timeout)
        if response.ok:
            return True, response.elapsed.total_seconds()
        else:
            return False, None
    except (requests.exceptions.RequestException, requests.exceptions.Timeout):
        return False, None