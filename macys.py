from playwright_framework import PlayWrightFrameWork
from tool import Log, check_proxy
from playwright.sync_api import Response, Page
import config
import random
import time
import requests
import re


class MACYS(PlayWrightFrameWork):
    def __init__(self, log_instance: Log = None):
        super().__init__(log_instance)

        self.url = ''

        self.email = ""
        self.phone = ""
        self.proxy = None
        
        self.bag_count = 0

    def ok(self): ...
    
    def save(self):
        with open(config.save_file, 'a', encoding='utf-8') as f:
            f.write(f'{self.email}, {self.bag_count}\n')

    def idle(self, mesc):
        self.page.wait_for_timeout(mesc)

    def random_email(self):
        # characters = string.ascii_letters + string.digits
        # mail = ''.join(random.choices(characters, k=random.randint(6, 10)))
        # self.email = f'{mail}{config.email_suffix}'
        import os
        os.environ["no_proxy"] = "*"
        # url = 'http://47.250.141.77:5000/'
        url = config.email_api
        try:
            r = requests.get(url)
            if not r.ok:
                self.log.err(f'邮箱获取失败: {r.text}')
                self.email = ''
            self.email = r.json().get('email')
            self.phone = r.json().get('phone_number')
            self.log.debug(f'邮箱获取成功: {self.email}')
            self.log.debug(f'手机号码获取成功: {self.phone}')
        except Exception as e:
            self.log.err(e)
        return self.email

    def random_phone(self):
        # area_code = random.randint(200, 999)
        # exchange_code = random.randint(200, 999)
        subscriber_number = random.randint(0, 9999)
        # self.phone = f"1{area_code:03d}{exchange_code:03d}{subscriber_number:04d}"
        self.phone = f'{self.phone[:6]}{subscriber_number:04d}'
        return self.phone
    
    def random_proxy(self):
        # return None
        while True:
            port = random.randint(config.proxy.get('start_port'), config.proxy.get('end_port'))
            proxy = f"{config.proxy.get('host')}:{port}"
            ok, delay = check_proxy(proxy)
            if ok:
                self.log.success(f'代理检测通过: {proxy}, {delay}s')
                self.proxy = proxy
                return True
            self.log.err(f'代理检测未通过: {proxy}')
                

    def focused_element(self):
        focused_element_info = self.page.evaluate(
            """() => {
                const element = document.activeElement;
                return {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    placeholder: element.placeholder,
                    value: element.value,
                    text: element.innerText || element.textContent
                };
                }"""
        )
        if focused_element_info:
            self.log.debug(f"焦点元素信息: {focused_element_info}")
            elem = self.page.evaluate_handle('document.activeElement')
            return elem, focused_element_info
        else:
            self.log.debug("当前没有焦点元素")
            return None, None
        

    def _hook_response(self, response: Response):
        flag = False
        if "api.bounceexchange.com/capture/submit" in response.url:
            flag = True
        if "api.bounceexchange.com/bounce/init1.js" in response.url:
            flag = True
        if "api.bounceexchange.com/bounce/reloadCampaigns.js" in response.url:
            flag = True
        if flag:
            if response.ok:
                self.log.success(f"status_code={response.status}, url={response.url}")
            else:
                self.log.err(f"status_code={response.status}, url={response.url}")
    
    @staticmethod
    def intercept_images(route):
        if route.request.resource_type == "image":
            # print(f"Blocked image: {route.request.url}")
            route.abort()
        else:
            route.continue_()
    
    def add_cart(self) -> int:
        page: Page = self.page
        _items = config.items.copy()
        random.shuffle(_items)
        _items = _items[:2]
        # page.route("**/*", self.intercept_images)
        for url in _items:
            try:
                self.log.debug(url)
                page.goto(url, wait_until="domcontentloaded")

                button_selector = 'button:has-text("Add To Bag")'
                self.log.notice(f"wait for selector: {button_selector}")

                # 尝试等待按钮出现，如果找不到就跳过当前商品
                try:
                    page.wait_for_selector(button_selector, state="visible", timeout=20000)
                except Exception as e:
                    self.log.err(f"找不到Add To Bag按钮，跳过商品: {url}")
                    continue  # 跳过当前商品，尝试下一个

                # 找到按钮，点击并结束函数
                try:
                    with page.expect_response('https://www.macys.com/xapi/bag/v1/add', timeout=20000) as response_info:
                        # 使用安全点击方法
                        click_success = False
                        if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                            click_success = self.ghost_cursor.safe_click_element(button_selector)
                        # 如果GhostCursor失败，使用普通点击
                        if not click_success:
                            page.click(button_selector, force=True)
                            self.log.notice(f"最终降级点击Add To Bag: {button_selector}")

                        #self.log.notice(f"clicked Add To Bag: {button_selector}")
                        # 等待页面响应
                        self.idle(5000)

                    response = response_info.value
                    self.log.success(f'{response.url}, {response.request.post_data}')
                    response_data = response.json()
                    self.bag_count = response_data['bag']['meta']['bagAttributes'].get('bagItemQuantity', 0)
                    self.bag_guid = response_data['bag']['meta'].get('bagGuid', '')
                    self.log.success(f'当前购物车数量: {self.bag_count}')
                    self.log.success(f"🔑 bagGuid: {self.bag_guid}")
                except Exception as e:
                    # 点击了按钮但没有监听到返回，也算成功
                    self.log.notice(f"点击了Add To Bag按钮，但未监听到返回: {e}")

                # 无论是否监听到返回，都结束函数
                self.log.notice("已点击Add To Bag按钮，结束add_cart函数")
                return

            except Exception as e:
                self.log.err(f"处理商品时出错: {e}")
                continue  # 继续尝试下一个商品
    

    def worker(self):
        try:
            page: Page = self.page
            page.add_init_script(
                """Object.defineProperties(navigator, {webdriver:{get:()=>undefined}}); delete navigator.__proto__.webdriver;"""
            )
            page.add_init_script(path="stealth.min.js")

            page.on("response", self._hook_response)
            
            urls = config.urls.copy()
            random.shuffle(urls)
            self.url = urls[0]

            self.log.notice(f"goto url: {self.url}")
            page.goto(self.url, wait_until="domcontentloaded")
            self.log.notice("wait_until = domcontentloaded Done.")

            # 监听弹窗出现的网络请求
            popup_appeared = False

            def handle_response(response):
                nonlocal popup_appeared
                if 'events.bouncex.net/track.gif/experiment' in response.url:
                    self.log.success(f"检测到弹窗出现信号: {response.url}")
                    popup_appeared = True

            # 设置响应监听器
            page.on("response", handle_response)

            # 等待弹窗出现信号
            self.log.notice("等待弹窗出现信号...")
            wait_time = 0
            max_wait_time = 60  # 最多等待30秒

            while not popup_appeared and wait_time < max_wait_time:
                self.idle(1000)  # 等待1秒
                wait_time += 1
                if wait_time % 5 == 0:  # 每5秒输出一次日志
                    self.log.debug(f"已等待 {wait_time} 秒，继续等待弹窗信号...")

            if not popup_appeared:
                self.log.err(f"等待 {max_wait_time} 秒后仍未检测到弹窗信号")
                # 移除监听器
                page.remove_listener("response", handle_response)
                return

            self.log.success("弹窗已出现，开始查找按钮...")

            # 移除监听器
            page.remove_listener("response", handle_response)

            # 等待一小段时间让弹窗完全加载
            self.idle(2000)

            # 查找按钮
            button_selector = 'button:has-text("Click to Claim 25% Off")'
            self.log.notice(f"wait for selector: {button_selector}")

            try:
                page.wait_for_selector(button_selector, state="visible", timeout=10000)
                self.log.notice(f"找到按钮: {button_selector}")
            except Exception as e:
                self.log.err(f"弹窗出现后仍未找到按钮: {e}")
                return

            # 使用安全点击方法
            if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                click_success = self.ghost_cursor.safe_click_element(button_selector)

            # 如果GhostCursor失败或不可用，使用普通点击
            if not click_success:
                try:
                    page.click(button_selector, force=True, timeout=10000)
                    click_success = True
                    self.log.notice(f"最终降级点击成功: {button_selector}")
                except Exception as e:
                    self.log.err(f"所有点击方法都失败了: {e}")
                    return

            self.log.notice(f"clicked: {button_selector}")
            # 等待页面响应
            self.idle(1000)
            
            _exit = 0            
            old_elem = None
            phone_elem = None
            while True:
                _exit += 1
                if _exit > 15:
                    self.log.err(f'_exit = {_exit}')
                    break
                page.keyboard.press('Tab')
                self.idle(1342)
                elem, elem_info = self.focused_element()
                if elem == old_elem:
                    page.keyboard.press('Tab')
                else:
                    old_elem = elem
                    
                if elem is None:
                    continue
                if elem_info['tagName'] == 'INPUT' and elem_info['placeholder'] == 'Your Email Here':
                    elem.as_element().fill('')
                    # 使用GhostCursor进行更自然的输入
                    if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                        self.ghost_cursor.human_type(self.email)
                    else:
                        page.keyboard.type(self.email, delay=220)
                    self.log.notice(f"邮箱已填写: {self.email}")
                if elem_info['tagName'] == 'INPUT' and elem_info['placeholder'] == 'Phone Number':
                    if phone_elem is None:
                        phone_elem = elem
                    phone_elem.as_element().fill('')
                    phone_number = self.random_phone()
                    # 使用GhostCursor进行更自然的输入
                    if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                        self.ghost_cursor.human_type(phone_number)
                    else:
                        page.keyboard.type(phone_number, delay=320)
                    self.log.notice(f"手机号码已填写: {self.phone}")
                    
                if elem_info['tagName'] == 'BUTTON' and 'Get 25% Off' in elem_info['text']:
                    with page.expect_response('https://api.bounceexchange.com/capture/submit*') as response_info:
                        # 使用GhostCursor进行更自然的点击
                        if hasattr(self, 'ghost_cursor') and self.ghost_cursor:
                            # 获取按钮的选择器
                            button_id = elem_info.get('id', '')
                            if button_id:
                                self.ghost_cursor.click_element(f'#{button_id}')
                            else:
                                elem.as_element().click()
                        else:
                            elem.as_element().click()
                    response = response_info.value
                    self.log.debug(response.text())
                    # break
                
                if elem_info['tagName'] == 'BUTTON' and 'Sign Up for Texts' in elem_info['text']:
                    with page.expect_response('https://api.bounceexchange.com/capture/submit*') as response_info:
                        if phone_elem:
                            phone_elem.as_element().fill(self.random_phone())
                        elem.as_element().click()
                    response = response_info.value
                    self.log.debug(response.text())
                    # if 'Please enter a valid phone number' not in response.text:
                    #     break
                
                if elem_info['tagName'] == 'BUTTON' and 'Continue Shopping' in elem_info['text']:
                    with page.expect_response('https://events.bouncex.net/track.gif/reloadcampaigns*') as response_info:
                        elem.as_element().click()
                    response = response_info.value
                    self.log.notice(f'_exit = {_exit}')
                    break
            
            self.add_cart()
            self.log.debug(f'当前购物车数量: {self.bag_count}')
            if self.bag_count > 0:
                self.save()
                self.bag_guid()
            
            page.remove_listener("response", self._hook_response)

        except Exception as e:
            import traceback
            self.log.err(e)
            traceback.print_exc()


if __name__ == "__main__":
    for i in range(config.CNT):
        print(f'========= {i} ==========')
        try:
            with MACYS(Log("main.log")) as macys:
                if macys.random_email() == '':
                    continue
                macys.random_proxy()
                server_proxy = {
                    'server': macys.proxy
                }
                if macys.new_chrome_browser(proxy=server_proxy):
                    macys.worker()
        except Exception as e:
            Log().err(e)
        if i < config.CNT - 1:
            time.sleep(config.interval)
